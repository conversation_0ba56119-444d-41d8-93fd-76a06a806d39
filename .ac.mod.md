# AC Module Documentation Generator

一个独立的功能组件，用于自动生成和维护 Auto-Coder 项目中的 .ac.mod.md 模块文档文件。

## 目录结构

```
src/autocoder/ac_mod_generator/
├── __init__.py                 # 模块初始化文件，导出主要类
├── generator.py                # 核心生成器类，协调整个文档生成过程
├── template_engine.py          # 模板引擎，负责渲染标准化文档结构
├── module_scanner.py           # 模块扫描器，分析代码结构和依赖关系
├── file_utils.py               # 文件操作工具类，提供安全的文件读写功能
├── cli.py                      # 命令行接口，提供用户友好的操作界面
├── tests/                      # 测试目录
│   ├── __init__.py             # 测试包初始化
│   ├── test_generator.py       # 生成器类测试
│   ├── test_module_scanner.py  # 模块扫描器测试
│   └── test_template_engine.py # 模板引擎测试
└── .ac.mod.md                  # 本文档
```

## 快速开始

### 基本使用方式

```python
# 导入必要的模块
from autocoder.ac_mod_generator import ACModGenerator

# 1. 初始化生成器
generator = ACModGenerator(root_path="./src")

# 2. 发现所有潜在模块
modules = generator.discover_modules()
print(f"Found {len(modules)} potential modules")

# 3. 为单个模块生成文档
module_path = Path("./src/my_module")
success = generator.generate_documentation(module_path)

# 4. 批量生成所有模块文档
results = generator.generate_all_documentation(force=False)
```

### 命令行使用方式

```bash
# 安装后可直接使用命令行工具
# 为特定模块生成文档
python -m autocoder.ac_mod_generator.cli generate ./src/my_module

# 批量生成所有模块文档
python -m autocoder.ac_mod_generator.cli generate-all --root-path ./src

# 列出所有潜在模块
python -m autocoder.ac_mod_generator.cli list

# 更新现有文档
python -m autocoder.ac_mod_generator.cli update ./src/my_module

# 使用自定义模板
python -m autocoder.ac_mod_generator.cli generate ./src/my_module --template ./custom_template.md
```

### 辅助函数说明

**模块发现功能:**
- `find_potential_modules()`: 扫描目录树，识别潜在的 AC 模块
- `get_directory_structure()`: 生成模块的目录结构树
- `extract_core_components()`: 分析 Python 代码，提取类和函数信息

**文档生成功能:**
- `render()`: 基于分析结果渲染完整的 .ac.mod.md 文档
- `update_existing()`: 更新现有文档，保留手动修改的内容
- `generate_documentation()`: 完整的文档生成流程

### 配置管理

组件支持以下配置选项：

- **root_path**: 项目根目录路径
- **template_path**: 自定义模板文件路径
- **force**: 是否覆盖现有文档
- **recursive**: 是否递归扫描子目录

## 核心组件详解

### 1. ACModGenerator 主生成器类

**核心特性:**
- 模块发现: 自动识别项目中的潜在 AC 模块
- 智能分析: 提取模块结构、依赖关系和使用示例
- 文档生成: 基于标准模板生成规范化文档
- 增量更新: 支持更新现有文档而不丢失手动修改

**主要方法:**
- `discover_modules()`: 发现所有潜在的 AC 模块
- `analyze_module(module_path)`: 深度分析单个模块的结构和元数据
- `generate_documentation(module_path, force=False)`: 为指定模块生成文档
- `generate_all_documentation(force=False)`: 批量生成所有模块文档
- `update_existing_documentation(module_path)`: 更新现有文档

### 2. ModuleScanner 模块扫描器

**核心特性:**
- 代码分析: 使用 AST 解析 Python 代码结构
- 依赖发现: 自动识别模块间的依赖关系
- 示例提取: 从测试文件和示例文件中提取使用模式
- 智能过滤: 排除测试文件、隐藏目录等非核心内容

**主要方法:**
- `find_potential_modules()`: 扫描并识别潜在模块
- `get_directory_structure(module_path)`: 生成目录结构树
- `extract_core_components(module_path)`: 提取类和函数信息
- `find_dependencies(module_path)`: 查找对其他 AC 模块的依赖
- `find_usage_examples(module_path)`: 提取使用示例
- `find_test_commands(module_path)`: 发现测试命令

### 3. TemplateEngine 模板引擎

**核心特性:**
- 标准化模板: 提供符合 AC 模块规范的默认模板
- 自定义支持: 支持用户自定义模板文件
- 智能渲染: 根据分析结果动态生成文档内容
- 增量更新: 支持部分更新现有文档

**主要方法:**
- `render(analysis)`: 基于分析结果渲染完整文档
- `update_existing(existing_content, analysis)`: 更新现有文档内容
- `_render_directory_structure(structure)`: 渲染目录结构部分
- `_render_core_components(components)`: 渲染核心组件部分

### 4. FileUtils 文件工具类

**核心特性:**
- 安全操作: 提供安全的文件读写操作
- 路径处理: 智能处理相对路径和绝对路径
- 错误处理: 完善的异常处理和日志记录
- 跨平台: 支持不同操作系统的文件操作

**主要方法:**
- `read_file(file_path)`: 安全读取文件内容
- `write_file(file_path, content)`: 安全写入文件内容
- `list_files(directory, pattern)`: 列出匹配模式的文件
- `find_files_by_extension(directory, extensions)`: 按扩展名查找文件

## 依赖关系说明

该组件为独立模块，不依赖其他 AC 模块，但可以与以下组件协同工作：

- 可被 `src/autocoder/common/v2/agent/.ac.mod.md` 中的 AC 模块工具调用
- 支持与现有的 `ACModWriteTool` 和 `ACModReadTool` 集成
- 兼容项目中现有的 `.autocodercommands/ac_mod.md` 工作流程

## 可以验证模块可运行的测试命令

```bash
# 运行所有测试
pytest src/autocoder/ac_mod_generator/tests/ -v

# 运行特定测试文件
pytest src/autocoder/ac_mod_generator/tests/test_generator.py -v

# 运行测试并显示覆盖率
pytest src/autocoder/ac_mod_generator/tests/ --cov=src/autocoder/ac_mod_generator --cov-report=html

# 测试命令行接口
python -m autocoder.ac_mod_generator.cli --help

# 实际生成测试
python -m autocoder.ac_mod_generator.cli generate-all --root-path ./src/autocoder/ac_mod_generator --yes
```
