├── BUILD
├── pom.xml
├── src
│   ├── main
│   │   └── java
│   │       └── com
│   │           └── daip
│   │               └── manager
│   │                   └── patcher
│   │                       ├── config
│   │                       ├── controller
│   │                       ├── domain
│   │                       │   ├── common/java/com/zte/daip/manager/patcher/domain/common/
│   │                       │   ├── PatchDispatchService.java
│   │                       │   ├── PatchDispatchThreadPool.java
│   │                       │   ├── PatchesQueryThreadPool.java
│   │                       │   ├── PatchException.java
│   │                       │   ├── PatchHistoryService.java
│   │                       │   ├── PatchInfoService.java
│   │                       │   ├── PatchRollbackService.java
│   │                       │   ├── PatchTaskService.java
│   │                       │   └── PatchThreadPool.java
│   └── test
│       ├── java
│       └── resources
└── target
    ├── classes
    │   └── com
    ├── generated-sources
    │   └── annotations
    ├── generated-test-sources
    │   └── test-annotations
    └── test-classes
        ├── application.yml
        ├── patch
        ├── schema
        ├── taskmodel
        └── upload
