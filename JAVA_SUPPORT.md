# Java 项目支持指南

AC 模块文档生成器现在完全支持 Java 项目！本指南将帮助您在 Java 项目中使用这个组件。

## 🚀 快速开始

### 1. 基本使用

```python
from pathlib import Path
from autocoder.ac_mod_generator import ACModGenerator

# 创建生成器实例
java_project_path = Path("/path/to/your/java/project")
generator = ACModGenerator(java_project_path.parent)

# 生成文档
success = generator.generate_documentation(java_project_path)
if success:
    print("✅ Java 项目文档生成成功！")
```

### 2. 命令行使用

```bash
# 为单个 Java 模块生成文档
ac-mod-gen generate /path/to/java/module

# 为大型 Java 模块生成分层文档（推荐）
ac-mod-gen generate /path/to/large/java/module --hierarchical

# 为所有发现的模块生成分层文档
ac-mod-gen generate-all --hierarchical --root-path /path/to/project

# 为所有发现的模块生成文档（包括 Java 和 Python）
ac-mod-gen generate-all --root-path /path/to/project

# 列出所有潜在的模块
ac-mod-gen list --root-path /path/to/project
```

## ☕ Java 项目支持特性

### 🔍 自动检测功能

- **Maven 项目**: 自动识别包含 `pom.xml` 的项目
- **Gradle 项目**: 支持 `build.gradle` 和 `build.gradle.kts`
- **标准结构**: 识别 `src/main/java` 和 `src/test/java` 目录结构
- **混合项目**: 同时支持 Java 和 Python 代码的项目

### 🔧 代码分析能力

- **类和接口**: 解析 Java 类、接口定义
- **方法提取**: 自动提取公共方法和构造函数
- **Javadoc 解析**: 提取 Javadoc 注释作为组件描述
- **包结构**: 理解 Java 包层次结构

### 📖 文档生成功能

- **使用示例**: 从 `Example*.java`、`Demo*.java` 文件提取示例
- **测试命令**: 自动生成 Maven (`mvn test`) 和 Gradle (`./gradlew test`) 测试命令
- **目录结构**: 生成清晰的项目目录树
- **组件文档**: 详细的类和方法文档
- **分层文档**: 为大型模块的子包生成独立文档，提供更好的粒度和可维护性
- **上下文限制**: 智能处理大型 Java 模块，防止上下文窗口超限

## 📁 支持的项目结构

### Maven 项目
```
my-java-module/
├── pom.xml                     # Maven 配置文件
├── src/
│   ├── main/
│   │   └── java/
│   │       └── com/example/
│   │           ├── Application.java
│   │           └── Utils.java
│   └── test/
│       └── java/
│           └── com/example/
│               └── ApplicationTest.java
└── .ac.mod.md                  # 生成的文档
```

### Gradle 项目
```
my-java-module/
├── build.gradle                # Gradle 配置文件
├── src/
│   ├── main/
│   │   └── java/
│   │       └── com/example/
│   │           └── MyClass.java
│   └── test/
│       └── java/
│           └── com/example/
│               └── MyClassTest.java
└── .ac.mod.md                  # 生成的文档
```

### 简单 Java 项目
```
my-java-module/
├── Main.java
├── Utils.java
├── ExampleUsage.java
├── MainTest.java
└── .ac.mod.md                  # 生成的文档
```

## 🎯 实际使用示例

### 示例 1: Maven 项目

假设您有一个 Maven 项目：

```bash
# 进入项目目录
cd /path/to/your/maven/project

# 生成文档
python -c "
from pathlib import Path
from autocoder.ac_mod_generator import ACModGenerator

project_path = Path('.')
generator = ACModGenerator(project_path)
success = generator.generate_documentation(project_path)
print('✅ 文档生成成功！' if success else '❌ 生成失败')
"
```

### 示例 2: 分层文档生成（推荐用于大型Java模块）

```python
from pathlib import Path
from autocoder.ac_mod_generator import ACModGenerator

# 为大型Java模块生成分层文档
java_module_path = Path("/path/to/large/java/module")
generator = ACModGenerator(java_module_path.parent)

# 生成分层文档（主模块 + 子包）
results = generator.generate_hierarchical_documentation(
    java_module_path,
    force=True
)

print(f"生成了 {len(results)} 个文档文件:")
for path, success in results.items():
    status = "✅" if success else "❌"
    print(f"  {status} {path}")
```

### 示例 3: 批量处理多个模块

```python
from pathlib import Path
from autocoder.ac_mod_generator import ACModGenerator

# 扫描整个项目
root_path = Path("/path/to/multi-module/project")
generator = ACModGenerator(root_path)

# 发现所有模块
modules = generator.discover_modules()
print(f"发现 {len(modules)} 个模块")

# 批量生成文档
results = generator.generate_all_documentation(force=True)
success_count = sum(1 for success in results.values() if success)
print(f"成功生成 {success_count}/{len(results)} 个模块的文档")
```

### 示例 4: 批量分层文档生成

```python
from pathlib import Path
from autocoder.ac_mod_generator import ACModGenerator

# 为所有Java模块生成分层文档
root_path = Path("/path/to/project")
generator = ACModGenerator(root_path)

# 批量生成分层文档
all_results = generator.generate_all_hierarchical_documentation(force=True)

print("分层文档生成完成:")
for main_module, results in all_results.items():
    print(f"\n{main_module}:")
    for path, success in results.items():
        status = "✅" if success else "❌"
        rel_path = "  └─ " + Path(path).name if path != main_module else "  ├─ (主模块)"
        print(f"    {status} {rel_path}")
```

## 🏗️ 分层文档生成

### 什么是分层文档生成？

分层文档生成是专为大型Java模块设计的功能，它会：

1. **主模块文档**: 在模块根目录生成总览文档
2. **子包文档**: 为有意义的子包生成独立的 `.ac.mod.md` 文件
3. **智能识别**: 自动识别值得单独文档化的功能包

### 适用场景

分层文档生成特别适合以下情况：

- **大型Java模块**: 包含多个功能包的复杂模块
- **微服务架构**: 每个服务包含多个层次（controller、service、domain等）
- **企业级应用**: 代码量大，需要更细粒度的文档管理

### 子包识别规则

工具会自动识别以下类型的子包：

1. **文件数量**: 包含3个或以上Java文件的包
2. **功能包名**: 常见的功能包名称，如：
   - `controller`, `service`, `domain`, `config`
   - `util`, `common`, `core`, `api`
   - `dto`, `entity`, `repository`, `dao`
   - `manager`, `handler`, `processor`, `factory`
3. **架构意义**: 包含接口或抽象类的包

### 生成的文档结构

对于典型的Java模块：

```
my-java-module/
├── pom.xml
├── .ac.mod.md                           # 主模块文档
├── src/main/java/com/example/
│   ├── config/
│   │   ├── DatabaseConfig.java
│   │   ├── SecurityConfig.java
│   │   └── .ac.mod.md                   # config包文档
│   ├── controller/
│   │   ├── UserController.java
│   │   ├── OrderController.java
│   │   ├── ProductController.java
│   │   └── .ac.mod.md                   # controller包文档
│   └── service/
│       ├── UserService.java
│       ├── OrderService.java
│       └── .ac.mod.md                   # service包文档
```

### 优势

1. **更精细的粒度**: 每个功能包都有专门的文档
2. **更好的维护性**: 修改某个包时只需更新对应文档
3. **清晰的架构**: 能够清楚展示模块的分层结构
4. **避免上下文限制**: 防止单个文档过于庞大

## 🔧 高级配置

### 自定义模板

您可以为 Java 项目创建自定义模板：

```python
# 使用自定义模板
generator = ACModGenerator(root_path, template_path="java_template.md")
```

### 过滤特定文件

组件会自动跳过以下文件和目录：
- 测试文件 (`*Test.java`)
- 构建目录 (`target/`, `build/`, `out/`)
- IDE 配置 (`.idea/`, `.vscode/`)

## 🧪 测试支持

### 自动检测测试框架

- **JUnit**: 识别 `*Test.java` 文件
- **Maven**: 生成 `mvn test` 命令
- **Gradle**: 生成 `./gradlew test` 命令

### 测试示例提取

从测试文件中提取使用示例：

```java
@Test
public void testExample() {
    // 这些代码会被提取为使用示例
    MyClass instance = new MyClass();
    String result = instance.process("data");
    assertEquals("expected", result);
}
```

## 🔄 与现有工具集成

### 与 Auto-Coder 集成

```python
# 在 Auto-Coder 工具中使用
from autocoder.ac_mod_generator import ACModGenerator

def generate_java_docs(project_path: str):
    generator = ACModGenerator(Path(project_path))
    return generator.generate_all_documentation()
```

### CI/CD 集成

```yaml
# GitHub Actions 示例
- name: Generate AC Module Documentation
  run: |
    pip install -e .
    python -c "
    from autocoder.ac_mod_generator import ACModGenerator
    from pathlib import Path
    generator = ACModGenerator(Path('.'))
    generator.generate_all_documentation(force=True)
    "
```

## ⚡ 大型 Java 模块的上下文限制功能

### 问题背景

大型 Java 项目通常包含数百个文件，在生成文档时可能会超过大语言模型的上下文窗口限制。为了解决这个问题，AC 模块生成器提供了智能的上下文限制功能。

### 主要特性

- **智能文件优先级**: 自动识别重要文件（主类、接口、服务类等）
- **代码片段提取**: 从大文件中提取相关代码片段而非完整内容
- **滑动窗口处理**: 对超大文件使用滑动窗口分析
- **多种裁剪策略**: 支持评分过滤、片段提取、简单删除三种策略

### 使用方法

#### 1. 检查模块上下文限制

```bash
# 检查 Java 模块是否超过上下文限制
python -m ac_mod_generator.cli check-context ./src/main/java/com/example/largemodule --show-files
```

```python
from ac_mod_generator import ACModGenerator
from pathlib import Path

generator = ACModGenerator()
result = generator.check_module_context_limits(
    Path("./src/main/java/com/example/largemodule"),
    max_tokens=32000
)

print(f"超过限制: {result['exceeds_limit']}")
print(f"总文件数: {result['total_files']}")
print(f"总 Token 数: {result['total_tokens']:,}")
```

#### 2. 生成带上下文限制的文档

```bash
# 启用上下文限制生成文档
python -m ac_mod_generator.cli generate ./src/main/java/com/example/largemodule \
    --enable-context-limiting --max-tokens 24000
```

```python
from ac_mod_generator import ACModGenerator
from ac_mod_generator.context_limiter import ContextLimitConfig

# 配置上下文限制
config = ContextLimitConfig(
    max_tokens=24000,           # 最大 Token 限制
    safe_zone_tokens=6000,      # 预留生成空间
    java_file_priority=True,    # Java 文件优先
    include_test_files=False,   # 排除测试文件
    max_files_per_type=30       # 每种类型最大文件数
)

generator = ACModGenerator(context_config=config)

# 生成文档
success = generator.generate_documentation_with_context_limiting(
    Path("./src/main/java/com/example/largemodule"),
    force=True,
    max_tokens=24000
)
```

### Java 文件优先级

对于 Java 项目，文件按以下优先级处理：

1. **主类**: `Application.java`, `Main.java`, 包含 `main` 方法的类
2. **接口**: 包含 `interface` 声明的文件
3. **服务类**: 名称包含 "Service" 的类
4. **控制器类**: 名称包含 "Controller" 的类
5. **模型类**: DTOs, entities, models
6. **工具类**: Helper 和 utility 类
7. **其他类**: 剩余的 Java 文件

### 配置选项

| 参数                 | 类型 | 默认值 | 说明                   |
| -------------------- | ---- | ------ | ---------------------- |
| `max_tokens`         | int  | 32000  | 最大 Token 限制        |
| `safe_zone_tokens`   | int  | 8000   | 预留给生成的 Token 数  |
| `java_file_priority` | bool | True   | 是否优先处理 Java 文件 |
| `include_test_files` | bool | False  | 是否包含测试文件       |
| `max_files_per_type` | int  | 50     | 每种文件类型的最大数量 |

### 最佳实践

#### 小型到中型 Java 模块 (< 20 文件)
```python
# 标准生成，无需上下文限制
generator = ACModGenerator()
generator.generate_documentation(module_path)
```

#### 大型 Java 模块 (20-100 文件)
```python
# 启用适度的上下文限制
config = ContextLimitConfig(max_tokens=24000, include_test_files=False)
generator = ACModGenerator(context_config=config)
generator.generate_documentation_with_context_limiting(module_path, max_tokens=24000)
```

#### 超大型 Java 模块 (100+ 文件)
```python
# 激进的上下文限制
config = ContextLimitConfig(
    max_tokens=16000,
    max_files_per_type=20,
    include_test_files=False
)
generator = ACModGenerator(context_config=config)
generator.generate_documentation_with_context_limiting(module_path, max_tokens=16000)
```

## 🎉 总结

现在您可以在 Java 项目中使用 AC 模块文档生成器了！这个组件提供了：

✅ **完整的 Java 支持** - Maven、Gradle、标准 Java 项目
✅ **智能代码分析** - 类、方法、Javadoc 解析
✅ **自动化文档生成** - 标准化的 `.ac.mod.md` 文件
✅ **测试集成** - 自动发现测试命令和示例
✅ **混合项目支持** - Java 和 Python 代码共存
✅ **上下文限制** - 智能处理大型模块，防止上下文超限

开始使用吧！🚀
