# Java 项目支持指南

AC 模块文档生成器现在完全支持 Java 项目！本指南将帮助您在 Java 项目中使用这个组件。

## 🚀 快速开始

### 1. 基本使用

```python
from pathlib import Path
from autocoder.ac_mod_generator import ACModGenerator

# 创建生成器实例
java_project_path = Path("/path/to/your/java/project")
generator = ACModGenerator(java_project_path.parent)

# 生成文档
success = generator.generate_documentation(java_project_path)
if success:
    print("✅ Java 项目文档生成成功！")
```

### 2. 命令行使用

```bash
# 为单个 Java 模块生成文档
ac-mod-gen generate /path/to/java/module

# 为所有发现的模块生成文档（包括 Java 和 Python）
ac-mod-gen generate-all --root-path /path/to/project

# 列出所有潜在的模块
ac-mod-gen list --root-path /path/to/project
```

## ☕ Java 项目支持特性

### 🔍 自动检测功能

- **Maven 项目**: 自动识别包含 `pom.xml` 的项目
- **Gradle 项目**: 支持 `build.gradle` 和 `build.gradle.kts`
- **标准结构**: 识别 `src/main/java` 和 `src/test/java` 目录结构
- **混合项目**: 同时支持 Java 和 Python 代码的项目

### 🔧 代码分析能力

- **类和接口**: 解析 Java 类、接口定义
- **方法提取**: 自动提取公共方法和构造函数
- **Javadoc 解析**: 提取 Javadoc 注释作为组件描述
- **包结构**: 理解 Java 包层次结构

### 📖 文档生成功能

- **使用示例**: 从 `Example*.java`、`Demo*.java` 文件提取示例
- **测试命令**: 自动生成 Maven (`mvn test`) 和 Gradle (`./gradlew test`) 测试命令
- **目录结构**: 生成清晰的项目目录树
- **组件文档**: 详细的类和方法文档

## 📁 支持的项目结构

### Maven 项目
```
my-java-module/
├── pom.xml                     # Maven 配置文件
├── src/
│   ├── main/
│   │   └── java/
│   │       └── com/example/
│   │           ├── Application.java
│   │           └── Utils.java
│   └── test/
│       └── java/
│           └── com/example/
│               └── ApplicationTest.java
└── .ac.mod.md                  # 生成的文档
```

### Gradle 项目
```
my-java-module/
├── build.gradle                # Gradle 配置文件
├── src/
│   ├── main/
│   │   └── java/
│   │       └── com/example/
│   │           └── MyClass.java
│   └── test/
│       └── java/
│           └── com/example/
│               └── MyClassTest.java
└── .ac.mod.md                  # 生成的文档
```

### 简单 Java 项目
```
my-java-module/
├── Main.java
├── Utils.java
├── ExampleUsage.java
├── MainTest.java
└── .ac.mod.md                  # 生成的文档
```

## 🎯 实际使用示例

### 示例 1: Maven 项目

假设您有一个 Maven 项目：

```bash
# 进入项目目录
cd /path/to/your/maven/project

# 生成文档
python -c "
from pathlib import Path
from autocoder.ac_mod_generator import ACModGenerator

project_path = Path('.')
generator = ACModGenerator(project_path)
success = generator.generate_documentation(project_path)
print('✅ 文档生成成功！' if success else '❌ 生成失败')
"
```

### 示例 2: 批量处理多个模块

```python
from pathlib import Path
from autocoder.ac_mod_generator import ACModGenerator

# 扫描整个项目
root_path = Path("/path/to/multi-module/project")
generator = ACModGenerator(root_path)

# 发现所有模块
modules = generator.discover_modules()
print(f"发现 {len(modules)} 个模块")

# 批量生成文档
results = generator.generate_all_documentation(force=True)
success_count = sum(1 for success in results.values() if success)
print(f"成功生成 {success_count}/{len(results)} 个模块的文档")
```

## 🔧 高级配置

### 自定义模板

您可以为 Java 项目创建自定义模板：

```python
# 使用自定义模板
generator = ACModGenerator(root_path, template_path="java_template.md")
```

### 过滤特定文件

组件会自动跳过以下文件和目录：
- 测试文件 (`*Test.java`)
- 构建目录 (`target/`, `build/`, `out/`)
- IDE 配置 (`.idea/`, `.vscode/`)

## 🧪 测试支持

### 自动检测测试框架

- **JUnit**: 识别 `*Test.java` 文件
- **Maven**: 生成 `mvn test` 命令
- **Gradle**: 生成 `./gradlew test` 命令

### 测试示例提取

从测试文件中提取使用示例：

```java
@Test
public void testExample() {
    // 这些代码会被提取为使用示例
    MyClass instance = new MyClass();
    String result = instance.process("data");
    assertEquals("expected", result);
}
```

## 🔄 与现有工具集成

### 与 Auto-Coder 集成

```python
# 在 Auto-Coder 工具中使用
from autocoder.ac_mod_generator import ACModGenerator

def generate_java_docs(project_path: str):
    generator = ACModGenerator(Path(project_path))
    return generator.generate_all_documentation()
```

### CI/CD 集成

```yaml
# GitHub Actions 示例
- name: Generate AC Module Documentation
  run: |
    pip install -e .
    python -c "
    from autocoder.ac_mod_generator import ACModGenerator
    from pathlib import Path
    generator = ACModGenerator(Path('.'))
    generator.generate_all_documentation(force=True)
    "
```

## 🎉 总结

现在您可以在 Java 项目中使用 AC 模块文档生成器了！这个组件提供了：

✅ **完整的 Java 支持** - Maven、Gradle、标准 Java 项目  
✅ **智能代码分析** - 类、方法、Javadoc 解析  
✅ **自动化文档生成** - 标准化的 `.ac.mod.md` 文件  
✅ **测试集成** - 自动发现测试命令和示例  
✅ **混合项目支持** - Java 和 Python 代码共存  

开始使用吧！🚀
