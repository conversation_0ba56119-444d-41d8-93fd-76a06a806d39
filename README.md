# AC Module Documentation Generator

一个独立的功能组件，用于自动生成和维护 Auto-Coder 项目中的 `.ac.mod.md` 模块文档文件。

## 特性

- 🔍 **智能模块发现**: 自动扫描项目目录，识别潜在的 AC 模块
- 📊 **深度代码分析**: 使用 AST 解析 Python 代码，提取类、函数和文档字符串
- 🔗 **依赖关系分析**: 自动发现模块间的依赖关系
- 📝 **标准化文档**: 生成符合 AC 模块规范的标准化文档
- 🔄 **增量更新**: 支持更新现有文档而不丢失手动修改
- 🎨 **自定义模板**: 支持用户自定义文档模板
- 🖥️ **命令行工具**: 提供友好的命令行接口
- ✅ **完整测试**: 包含全面的单元测试和集成测试

## 快速开始

### 安装

该组件是 Auto-Coder 项目的一部分，无需单独安装。

### 基本使用

#### 1. 编程接口

```python
from autocoder.ac_mod_generator import ACModGenerator
from pathlib import Path

# 初始化生成器
generator = ACModGenerator(root_path="./src")

# 发现所有潜在模块
modules = generator.discover_modules()
print(f"发现 {len(modules)} 个潜在模块")

# 为单个模块生成文档
module_path = Path("./src/my_module")
success = generator.generate_documentation(module_path)
if success:
    print(f"✓ 成功生成文档: {module_path}")

# 批量生成所有模块文档
results = generator.generate_all_documentation(force=False)
success_count = sum(1 for success in results.values() if success)
print(f"成功生成 {success_count}/{len(results)} 个模块的文档")
```

#### 2. 命令行接口

```bash
# 查看帮助
python -m autocoder.ac_mod_generator.cli --help

# 列出所有潜在模块
python -m autocoder.ac_mod_generator.cli list

# 为特定模块生成文档
python -m autocoder.ac_mod_generator.cli generate ./src/my_module

# 批量生成所有模块文档
python -m autocoder.ac_mod_generator.cli generate-all --root-path ./src

# 更新现有文档
python -m autocoder.ac_mod_generator.cli update ./src/my_module

# 使用自定义模板
python -m autocoder.ac_mod_generator.cli generate ./src/my_module --template ./custom_template.md

# 强制覆盖现有文档
python -m autocoder.ac_mod_generator.cli generate-all --force --yes
```

## 高级用法

### 自定义模板

创建自定义模板文件 `custom_template.md`:

```markdown
# {{MODULE_NAME}}

{{MODULE_DESCRIPTION}}

## 模块结构

{{DIRECTORY_STRUCTURE}}

## 核心功能

{{CORE_COMPONENTS}}

## 使用示例

{{USAGE_EXAMPLES}}

## 依赖模块

{{DEPENDENCIES}}

## 测试方法

{{TEST_COMMANDS}}
```

然后使用自定义模板：

```python
generator = ACModGenerator(root_path="./src", template_path="./custom_template.md")
```

### 集成到现有工具

该组件可以轻松集成到现有的 Auto-Coder 工具链中：

```python
# 与现有的 ACModWriteTool 集成
from autocoder.ac_mod_generator import ACModGenerator

def enhanced_ac_mod_write(module_path: str):
    generator = ACModGenerator()
    
    # 先生成基础文档
    generator.generate_documentation(Path(module_path))
    
    # 然后可以进行进一步的自定义修改
    # ...
```

## 配置选项

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `root_path` | str/Path | "." | 项目根目录路径 |
| `template_path` | str/None | None | 自定义模板文件路径 |
| `force` | bool | False | 是否覆盖现有文档 |
| `recursive` | bool | True | 是否递归扫描子目录 |

## 文档结构

生成的 `.ac.mod.md` 文件包含以下标准化章节：

1. **模块标题和描述**: 模块名称和一句话功能描述
2. **目录结构**: 可视化的目录树，包含文件功能说明
3. **快速开始**: 基本使用方式、辅助函数说明、配置管理
4. **核心组件详解**: 主要类和函数的详细说明
5. **依赖关系说明**: 对其他 AC 模块的依赖列表
6. **测试命令**: 可执行的验证命令

## 开发和测试

### 运行测试

```bash
# 运行所有测试
pytest src/autocoder/ac_mod_generator/tests/ -v

# 运行特定测试
pytest src/autocoder/ac_mod_generator/tests/test_generator.py -v

# 生成测试覆盖率报告
pytest src/autocoder/ac_mod_generator/tests/ --cov=src/autocoder/ac_mod_generator --cov-report=html
```

### 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建 Pull Request

## 许可证

该组件遵循 Auto-Coder 项目的许可证。

## 更新日志

### v1.0.0
- 初始版本发布
- 支持基本的模块发现和文档生成
- 提供命令行接口
- 包含完整的测试套件
