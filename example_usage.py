#!/usr/bin/env python3
"""
Example usage of AC Module Documentation Generator

This script demonstrates various ways to use the AC Module Generator
for generating and maintaining .ac.mod.md files.
"""

import logging
from pathlib import Path
from autocoder.ac_mod_generator import ACModGenerator, ModuleScanner, Template<PERSON>ngine, FileUtils


def setup_logging():
    """Setup logging for the example."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )


def example_basic_usage():
    """Example 1: Basic usage - generate documentation for a single module."""
    print("=== Example 1: Basic Usage ===")
    
    # Initialize generator with current directory as root
    generator = ACModGenerator(root_path=".")
    
    # Generate documentation for this module itself
    module_path = Path("src/autocoder/ac_mod_generator")
    
    if module_path.exists():
        print(f"Generating documentation for: {module_path}")
        success = generator.generate_documentation(module_path, force=True)
        
        if success:
            print("✓ Documentation generated successfully!")
            doc_path = module_path / ".ac.mod.md"
            print(f"  Documentation saved to: {doc_path}")
        else:
            print("✗ Failed to generate documentation")
    else:
        print(f"Module path does not exist: {module_path}")


def example_discover_modules():
    """Example 2: Discover all potential modules in the project."""
    print("\n=== Example 2: Module Discovery ===")
    
    generator = ACModGenerator(root_path="src")
    
    print("Discovering potential modules...")
    modules = generator.discover_modules()
    
    print(f"Found {len(modules)} potential modules:")
    for i, module in enumerate(modules, 1):
        rel_path = module.relative_to(Path("src")) if module.is_relative_to(Path("src")) else module
        doc_exists = (module / ".ac.mod.md").exists()
        status = "📄" if doc_exists else "❌"
        print(f"  {i:2d}. {status} {rel_path}")
    
    print("\n📄 = has .ac.mod.md, ❌ = missing .ac.mod.md")


def example_batch_generation():
    """Example 3: Batch generate documentation for all modules."""
    print("\n=== Example 3: Batch Generation ===")
    
    generator = ACModGenerator(root_path="src")
    
    print("Generating documentation for all discovered modules...")
    results = generator.generate_all_documentation(force=False)
    
    success_count = sum(1 for success in results.values() if success)
    total_count = len(results)
    
    print(f"\nGeneration complete: {success_count}/{total_count} successful")
    
    for module_path, success in results.items():
        status = "✓" if success else "✗"
        rel_path = Path(module_path).relative_to(Path("src")) if Path(module_path).is_relative_to(Path("src")) else module_path
        print(f"  {status} {rel_path}")


def example_custom_template():
    """Example 4: Using a custom template."""
    print("\n=== Example 4: Custom Template ===")
    
    # Create a simple custom template
    custom_template = """# {{MODULE_NAME}} - Custom Template

{{MODULE_DESCRIPTION}}

## 📁 Directory Structure
{{DIRECTORY_STRUCTURE}}

## 🔧 Core Components
{{CORE_COMPONENTS}}

## 📖 Usage Examples
{{USAGE_EXAMPLES}}

## 🔗 Dependencies
{{DEPENDENCIES}}

## 🧪 Testing
{{TEST_COMMANDS}}

---
*Generated with custom template*
"""
    
    # Save custom template
    template_path = Path("custom_template.md")
    template_path.write_text(custom_template)
    
    try:
        # Use custom template
        generator = ACModGenerator(root_path=".", template_path=str(template_path))
        
        # Generate documentation with custom template
        module_path = Path("src/autocoder/ac_mod_generator")
        if module_path.exists():
            print(f"Generating documentation with custom template...")
            success = generator.generate_documentation(module_path, force=True)
            
            if success:
                print("✓ Custom template documentation generated!")
                doc_path = module_path / ".ac.mod.md"
                print(f"  Check the custom formatting in: {doc_path}")
            else:
                print("✗ Failed to generate documentation with custom template")
    
    finally:
        # Clean up custom template file
        if template_path.exists():
            template_path.unlink()
            print(f"  Cleaned up custom template: {template_path}")


def example_update_existing():
    """Example 5: Update existing documentation."""
    print("\n=== Example 5: Update Existing Documentation ===")
    
    generator = ACModGenerator(root_path=".")
    
    module_path = Path("src/autocoder/ac_mod_generator")
    doc_path = module_path / ".ac.mod.md"
    
    if doc_path.exists():
        print(f"Updating existing documentation: {doc_path}")
        
        # Read original content
        original_size = doc_path.stat().st_size
        
        success = generator.update_existing_documentation(module_path)
        
        if success:
            new_size = doc_path.stat().st_size
            print(f"✓ Documentation updated successfully!")
            print(f"  Size changed: {original_size} → {new_size} bytes")
        else:
            print("✗ Failed to update documentation")
    else:
        print(f"No existing documentation found at: {doc_path}")


def example_component_analysis():
    """Example 6: Detailed component analysis."""
    print("\n=== Example 6: Component Analysis ===")
    
    scanner = ModuleScanner(root_path=".")
    module_path = Path("src/autocoder/ac_mod_generator")
    
    if module_path.exists():
        print(f"Analyzing module: {module_path}")
        
        # Get directory structure
        print("\n📁 Directory Structure:")
        structure = scanner.get_directory_structure(module_path)
        print(f"  Root: {structure['name']}")
        for child in structure.get('children', [])[:5]:  # Show first 5 items
            print(f"    - {child['name']}")
        if len(structure.get('children', [])) > 5:
            print(f"    ... and {len(structure['children']) - 5} more items")
        
        # Get core components
        print("\n🔧 Core Components:")
        components = scanner.extract_core_components(module_path)
        for i, component in enumerate(components[:3], 1):  # Show first 3 components
            print(f"  {i}. {component['name']} ({component['type']})")
            if component.get('description'):
                print(f"     {component['description'][:60]}...")
        if len(components) > 3:
            print(f"  ... and {len(components) - 3} more components")
        
        # Get dependencies
        print("\n🔗 Dependencies:")
        dependencies = scanner.find_dependencies(module_path)
        if dependencies:
            for dep in dependencies[:3]:  # Show first 3 dependencies
                print(f"  - {dep}")
            if len(dependencies) > 3:
                print(f"  ... and {len(dependencies) - 3} more dependencies")
        else:
            print("  No dependencies found")
        
        # Get test commands
        print("\n🧪 Test Commands:")
        test_commands = scanner.find_test_commands(module_path)
        for cmd in test_commands:
            print(f"  - {cmd}")


def main():
    """Run all examples."""
    setup_logging()
    
    print("AC Module Documentation Generator - Usage Examples")
    print("=" * 55)
    
    try:
        example_basic_usage()
        example_discover_modules()
        example_batch_generation()
        example_custom_template()
        example_update_existing()
        example_component_analysis()
        
        print("\n" + "=" * 55)
        print("All examples completed successfully!")
        print("\nNext steps:")
        print("1. Check the generated .ac.mod.md files")
        print("2. Try the command line interface:")
        print("   python -m autocoder.ac_mod_generator.cli --help")
        print("3. Run the tests:")
        print("   pytest src/autocoder/ac_mod_generator/tests/ -v")
        
    except Exception as e:
        print(f"\n❌ Error running examples: {e}")
        logging.exception("Error in example execution")


if __name__ == "__main__":
    main()
