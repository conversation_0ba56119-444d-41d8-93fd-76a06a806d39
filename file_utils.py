"""
File Utilities for AC Module Generator

This module provides file and directory manipulation utilities
for the AC module documentation generator.
"""

import os
import shutil
from pathlib import Path
from typing import List, Optional, Dict, Any
import logging

logger = logging.getLogger(__name__)


class FileUtils:
    """
    Utility class for file and directory operations.
    
    Provides safe file operations with proper error handling
    and logging for the AC module generator.
    """
    
    @staticmethod
    def read_file(file_path: Path, encoding: str = 'utf-8') -> str:
        """
        Read content from a file.
        
        Args:
            file_path: Path to the file to read
            encoding: File encoding (default: utf-8)
            
        Returns:
            File content as string
            
        Raises:
            FileNotFoundError: If file doesn't exist
            IOError: If file cannot be read
        """
        try:
            with open(file_path, 'r', encoding=encoding, errors='replace') as f:
                return f.read()
        except Exception as e:
            logger.error(f"Failed to read file {file_path}: {e}")
            raise
    
    @staticmethod
    def write_file(file_path: Path, content: str, encoding: str = 'utf-8') -> bool:
        """
        Write content to a file.
        
        Args:
            file_path: Path to the file to write
            content: Content to write
            encoding: File encoding (default: utf-8)
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Create parent directories if they don't exist
            file_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(file_path, 'w', encoding=encoding) as f:
                f.write(content)
            
            logger.info(f"Successfully wrote file: {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to write file {file_path}: {e}")
            return False
    
    @staticmethod
    def list_files(directory: Path, pattern: str = "*", recursive: bool = True) -> List[Path]:
        """
        List files in a directory matching a pattern.
        
        Args:
            directory: Directory to search
            pattern: File pattern to match (default: "*")
            recursive: Whether to search recursively
            
        Returns:
            List of matching file paths
        """
        try:
            if recursive:
                return list(directory.rglob(pattern))
            else:
                return list(directory.glob(pattern))
        except Exception as e:
            logger.error(f"Failed to list files in {directory}: {e}")
            return []
    
    @staticmethod
    def list_directories(directory: Path, recursive: bool = True) -> List[Path]:
        """
        List subdirectories in a directory.
        
        Args:
            directory: Directory to search
            recursive: Whether to search recursively
            
        Returns:
            List of directory paths
        """
        try:
            if recursive:
                return [p for p in directory.rglob("*") if p.is_dir()]
            else:
                return [p for p in directory.iterdir() if p.is_dir()]
        except Exception as e:
            logger.error(f"Failed to list directories in {directory}: {e}")
            return []
    
    @staticmethod
    def get_file_info(file_path: Path) -> Dict[str, Any]:
        """
        Get information about a file.
        
        Args:
            file_path: Path to the file
            
        Returns:
            Dictionary containing file information
        """
        try:
            stat = file_path.stat()
            return {
                "name": file_path.name,
                "size": stat.st_size,
                "modified": stat.st_mtime,
                "is_file": file_path.is_file(),
                "is_dir": file_path.is_dir(),
                "suffix": file_path.suffix,
                "stem": file_path.stem,
            }
        except Exception as e:
            logger.error(f"Failed to get file info for {file_path}: {e}")
            return {}
    
    @staticmethod
    def find_files_by_extension(directory: Path, extensions: List[str]) -> List[Path]:
        """
        Find files with specific extensions.
        
        Args:
            directory: Directory to search
            extensions: List of file extensions (e.g., ['.py', '.md'])
            
        Returns:
            List of matching file paths
        """
        files = []
        for ext in extensions:
            if not ext.startswith('.'):
                ext = '.' + ext
            files.extend(FileUtils.list_files(directory, f"*{ext}"))
        return files
    
    @staticmethod
    def is_python_package(directory: Path) -> bool:
        """
        Check if a directory is a Python package.
        
        Args:
            directory: Directory to check
            
        Returns:
            True if directory contains __init__.py
        """
        return (directory / "__init__.py").exists()
    
    @staticmethod
    def is_git_repository(directory: Path) -> bool:
        """
        Check if a directory is a Git repository.

        Args:
            directory: Directory to check

        Returns:
            True if directory contains .git
        """
        return (directory / ".git").exists()

    @staticmethod
    def is_java_project(directory: Path) -> bool:
        """
        Check if a directory is a Java project.

        Args:
            directory: Directory to check

        Returns:
            True if directory contains Java project indicators
        """
        java_indicators = [
            'pom.xml',  # Maven
            'build.gradle',  # Gradle
            'build.gradle.kts',  # Gradle Kotlin DSL
            'src/main/java',  # Standard Maven/Gradle structure
        ]

        for indicator in java_indicators:
            if (directory / indicator).exists():
                return True

        # Check for Java files
        java_files = FileUtils.find_files_by_extension(directory, ['.java'])
        return len(java_files) > 0
    
    @staticmethod
    def get_relative_path(path: Path, base_path: Path) -> str:
        """
        Get relative path from base path.
        
        Args:
            path: Target path
            base_path: Base path
            
        Returns:
            Relative path as string
        """
        try:
            return str(path.relative_to(base_path))
        except ValueError:
            # If path is not relative to base_path, return absolute path
            return str(path)
    
    @staticmethod
    def backup_file(file_path: Path, backup_suffix: str = ".bak") -> Optional[Path]:
        """
        Create a backup of a file.
        
        Args:
            file_path: Path to the file to backup
            backup_suffix: Suffix for backup file
            
        Returns:
            Path to backup file if successful, None otherwise
        """
        try:
            if not file_path.exists():
                return None
            
            backup_path = file_path.with_suffix(file_path.suffix + backup_suffix)
            shutil.copy2(file_path, backup_path)
            
            logger.info(f"Created backup: {backup_path}")
            return backup_path
            
        except Exception as e:
            logger.error(f"Failed to backup file {file_path}: {e}")
            return None
    
    @staticmethod
    def safe_filename(filename: str) -> str:
        """
        Convert a string to a safe filename.
        
        Args:
            filename: Original filename
            
        Returns:
            Safe filename string
        """
        # Remove or replace unsafe characters
        unsafe_chars = '<>:"/\\|?*'
        safe_name = filename
        
        for char in unsafe_chars:
            safe_name = safe_name.replace(char, '_')
        
        # Remove leading/trailing whitespace and dots
        safe_name = safe_name.strip(' .')
        
        # Ensure it's not empty
        if not safe_name:
            safe_name = "unnamed"
        
        return safe_name
    
    @staticmethod
    def ensure_directory(directory: Path) -> bool:
        """
        Ensure a directory exists, creating it if necessary.
        
        Args:
            directory: Directory path
            
        Returns:
            True if directory exists or was created successfully
        """
        try:
            directory.mkdir(parents=True, exist_ok=True)
            return True
        except Exception as e:
            logger.error(f"Failed to create directory {directory}: {e}")
            return False
