"""
AC Module Documentation Generator Core Class

This module provides the main ACModGenerator class that orchestrates
the entire process of generating .ac.mod.md files.
"""

import os
import logging
from typing import List, Dict, Any, Optional
from pathlib import Path

try:
    from .module_scanner import ModuleScanner
    from .template_engine import TemplateEngine
    from .file_utils import FileUtils
except ImportError:
    from module_scanner import Module<PERSON>canner
    from template_engine import TemplateEngine
    from file_utils import FileUtils

logger = logging.getLogger(__name__)


class ACModGenerator:
    """
    Main class for generating AC module documentation.
    
    This class coordinates the entire process of:
    1. Discovering modules in a project
    2. Analyzing module structure and dependencies
    3. Generating standardized .ac.mod.md documentation
    """
    
    def __init__(self, root_path: str = ".", template_path: Optional[str] = None):
        """
        Initialize the AC Module Generator.
        
        Args:
            root_path: Root directory to scan for modules
            template_path: Optional custom template file path
        """
        self.root_path = Path(root_path).resolve()
        self.scanner = ModuleScanner(self.root_path)
        self.template_engine = TemplateEngine(template_path)
        self.file_utils = FileUtils()
        
    def discover_modules(self) -> List[Path]:
        """
        Discover all potential AC modules in the project.
        
        Returns:
            List of directory paths that could be AC modules
        """
        return self.scanner.find_potential_modules()
    
    def analyze_module(self, module_path: Path) -> Dict[str, Any]:
        """
        Analyze a single module and extract its structure and metadata.
        
        Args:
            module_path: Path to the module directory
            
        Returns:
            Dictionary containing module analysis results
        """
        logger.info(f"Analyzing module: {module_path}")
        
        analysis = {
            "name": module_path.name,
            "path": str(module_path.relative_to(self.root_path)),
            "description": "",
            "directory_structure": self.scanner.get_directory_structure(module_path),
            "core_components": self.scanner.extract_core_components(module_path),
            "dependencies": self.scanner.find_dependencies(module_path),
            "usage_examples": self.scanner.find_usage_examples(module_path),
            "test_commands": self.scanner.find_test_commands(module_path),
        }
        
        # Try to extract description from existing files
        analysis["description"] = self.scanner.extract_module_description(module_path)
        
        return analysis
    
    def generate_documentation(self, module_path: Path, force: bool = False) -> bool:
        """
        Generate .ac.mod.md documentation for a single module.
        
        Args:
            module_path: Path to the module directory
            force: Whether to overwrite existing documentation
            
        Returns:
            True if documentation was generated successfully
        """
        try:
            # Check if documentation already exists
            doc_path = module_path / ".ac.mod.md"
            if doc_path.exists() and not force:
                logger.info(f"Documentation already exists: {doc_path}")
                return False
            
            # Analyze the module
            analysis = self.analyze_module(module_path)
            
            # Generate documentation content
            content = self.template_engine.render(analysis)
            
            # Write the documentation file
            self.file_utils.write_file(doc_path, content)
            
            logger.info(f"Generated documentation: {doc_path}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to generate documentation for {module_path}: {e}")
            return False
    
    def generate_all_documentation(self, force: bool = False) -> Dict[str, bool]:
        """
        Generate documentation for all discovered modules.
        
        Args:
            force: Whether to overwrite existing documentation
            
        Returns:
            Dictionary mapping module paths to generation success status
        """
        modules = self.discover_modules()
        results = {}
        
        logger.info(f"Found {len(modules)} potential modules")
        
        for module_path in modules:
            success = self.generate_documentation(module_path, force)
            results[str(module_path)] = success
            
        return results
    
    def update_existing_documentation(self, module_path: Path) -> bool:
        """
        Update existing .ac.mod.md documentation with new analysis.
        
        Args:
            module_path: Path to the module directory
            
        Returns:
            True if documentation was updated successfully
        """
        doc_path = module_path / ".ac.mod.md"
        if not doc_path.exists():
            logger.warning(f"No existing documentation found: {doc_path}")
            return self.generate_documentation(module_path)
        
        try:
            # Read existing content
            existing_content = self.file_utils.read_file(doc_path)
            
            # Analyze current module state
            analysis = self.analyze_module(module_path)
            
            # Update content while preserving manual modifications
            updated_content = self.template_engine.update_existing(existing_content, analysis)
            
            # Write updated content
            self.file_utils.write_file(doc_path, updated_content)
            
            logger.info(f"Updated documentation: {doc_path}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to update documentation for {module_path}: {e}")
            return False
