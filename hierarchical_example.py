#!/usr/bin/env python3
"""
Hierarchical Documentation Generation Example

This example demonstrates the new hierarchical documentation generation feature
for large Java modules.
"""

import tempfile
import shutil
from pathlib import Path
from generator import ACModGenerator


def create_sample_java_project():
    """Create a sample Java project structure for demonstration."""
    # Create temporary directory
    temp_dir = Path(tempfile.mkdtemp())
    print(f"Creating sample Java project in: {temp_dir}")
    
    # Create main Java module
    java_module = temp_dir / "large-java-module"
    java_module.mkdir()
    (java_module / "pom.xml").write_text("""<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.example</groupId>
    <artifactId>large-java-module</artifactId>
    <version>1.0.0</version>
    <packaging>jar</packaging>
    <name>Large Java Module</name>
</project>""")
    
    # Create standard Maven structure
    src_main_java = java_module / "src" / "main" / "java" / "com" / "example" / "app"
    src_main_java.mkdir(parents=True)
    
    # Create main application class
    (src_main_java / "Application.java").write_text("""package com.example.app;

/**
 * Main application class for the large Java module.
 */
public class Application {
    public static void main(String[] args) {
        System.out.println("Large Java Module Application");
    }
}""")
    
    # Create controller package
    controller_pkg = src_main_java / "controller"
    controller_pkg.mkdir()
    
    (controller_pkg / "UserController.java").write_text("""package com.example.app.controller;

/**
 * REST controller for user management operations.
 */
public class UserController {
    public String getUser(Long id) {
        return "User " + id;
    }
    
    public String createUser(String name) {
        return "Created user: " + name;
    }
}""")
    
    (controller_pkg / "OrderController.java").write_text("""package com.example.app.controller;

/**
 * REST controller for order management operations.
 */
public class OrderController {
    public String getOrder(Long id) {
        return "Order " + id;
    }
    
    public String createOrder(String details) {
        return "Created order: " + details;
    }
}""")
    
    (controller_pkg / "ProductController.java").write_text("""package com.example.app.controller;

/**
 * REST controller for product management operations.
 */
public class ProductController {
    public String getProduct(Long id) {
        return "Product " + id;
    }
    
    public String createProduct(String name, Double price) {
        return "Created product: " + name + " ($" + price + ")";
    }
}""")
    
    # Create service package
    service_pkg = src_main_java / "service"
    service_pkg.mkdir()
    
    (service_pkg / "UserService.java").write_text("""package com.example.app.service;

/**
 * Business logic service for user operations.
 */
public class UserService {
    public boolean validateUser(String username) {
        return username != null && !username.isEmpty();
    }
}""")
    
    (service_pkg / "OrderService.java").write_text("""package com.example.app.service;

/**
 * Business logic service for order operations.
 */
public class OrderService {
    public double calculateTotal(double amount, double tax) {
        return amount + (amount * tax);
    }
}""")
    
    # Create domain package
    domain_pkg = src_main_java / "domain"
    domain_pkg.mkdir()
    
    (domain_pkg / "User.java").write_text("""package com.example.app.domain;

/**
 * User domain entity.
 */
public class User {
    private Long id;
    private String name;
    private String email;
    
    // Getters and setters would be here
}""")
    
    (domain_pkg / "Order.java").write_text("""package com.example.app.domain;

/**
 * Order domain entity.
 */
public class Order {
    private Long id;
    private String details;
    private Double amount;
    
    // Getters and setters would be here
}""")
    
    (domain_pkg / "Product.java").write_text("""package com.example.app.domain;

/**
 * Product domain entity.
 */
public class Product {
    private Long id;
    private String name;
    private Double price;
    
    // Getters and setters would be here
}""")
    
    # Create config package
    config_pkg = src_main_java / "config"
    config_pkg.mkdir()
    
    (config_pkg / "DatabaseConfig.java").write_text("""package com.example.app.config;

/**
 * Database configuration class.
 */
public class DatabaseConfig {
    private String url;
    private String username;
    private String password;
    
    // Configuration methods would be here
}""")
    
    return temp_dir, java_module


def demonstrate_hierarchical_generation():
    """Demonstrate hierarchical documentation generation."""
    print("=== Hierarchical Documentation Generation Demo ===\n")
    
    # Create sample project
    temp_dir, java_module = create_sample_java_project()
    
    try:
        # Initialize generator
        generator = ACModGenerator(temp_dir)
        
        print("1. Standard module discovery (without subpackages):")
        standard_modules = generator.discover_modules(include_subpackages=False)
        for module in standard_modules:
            print(f"   - {module.relative_to(temp_dir)}")
        
        print(f"\n2. Module discovery with subpackages:")
        all_modules = generator.discover_modules(include_subpackages=True)
        for module in all_modules:
            print(f"   - {module.relative_to(temp_dir)}")
        
        print(f"\n3. Generating hierarchical documentation for: {java_module.name}")
        results = generator.generate_hierarchical_documentation(java_module, force=True)
        
        print(f"\nGeneration results:")
        for path, success in results.items():
            status = "✅" if success else "❌"
            rel_path = Path(path).relative_to(temp_dir)
            print(f"   {status} {rel_path}")
        
        print(f"\n4. Generated documentation files:")
        for path in results.keys():
            doc_file = Path(path) / ".ac.mod.md"
            if doc_file.exists():
                rel_path = doc_file.relative_to(temp_dir)
                print(f"   📄 {rel_path}")
        
        print(f"\n5. Demonstrating batch hierarchical generation:")
        all_results = generator.generate_all_hierarchical_documentation(force=True)
        
        total_modules = len(all_results)
        total_documents = sum(len(results) for results in all_results.values())
        successful_documents = sum(
            sum(1 for success in results.values() if success) 
            for results in all_results.values()
        )
        
        print(f"   Main modules processed: {total_modules}")
        print(f"   Total documents generated: {successful_documents}/{total_documents}")
        
        print(f"\n6. Documentation structure overview:")
        for main_module, module_results in all_results.items():
            rel_main = Path(main_module).relative_to(temp_dir)
            print(f"   📁 {rel_main}:")
            for doc_path, success in module_results.items():
                status = "✅" if success else "❌"
                if doc_path == main_module:
                    print(f"      {status} 📄 .ac.mod.md (main module)")
                else:
                    rel_sub = Path(doc_path).relative_to(Path(main_module))
                    print(f"      {status} 📄 {rel_sub}/.ac.mod.md")
        
    finally:
        # Cleanup
        shutil.rmtree(temp_dir)
        print(f"\nCleaned up temporary directory: {temp_dir}")


if __name__ == "__main__":
    demonstrate_hierarchical_generation()
