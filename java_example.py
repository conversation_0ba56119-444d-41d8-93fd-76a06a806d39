#!/usr/bin/env python3
"""
Example demonstrating how to use AC Module Generator with Java projects
"""

import sys
sys.path.append('.')

from pathlib import Path
from generator import ACModGenerator

def generate_java_project_documentation():
    """
    Example: Generate documentation for a Java project
    """
    print("🚀 Java 项目文档生成示例")
    print("=" * 50)
    
    # 假设您有一个 Java 项目路径
    # 这里我们使用当前目录作为示例，您可以替换为实际的 Java 项目路径
    java_project_path = Path("/path/to/your/java/project")
    
    # 如果路径不存在，我们创建一个示例说明
    if not java_project_path.exists():
        print("📝 使用说明：")
        print("请将 java_project_path 替换为您的实际 Java 项目路径")
        print()
        print("支持的 Java 项目结构：")
        print("1. Maven 项目 (包含 pom.xml)")
        print("2. Gradle 项目 (包含 build.gradle 或 build.gradle.kts)")
        print("3. 标准 Java 项目 (包含 .java 文件)")
        print()
        
        # 使用当前目录作为根路径进行演示
        root_path = Path.cwd().parent.parent.parent  # 回到项目根目录
        print(f"演示：扫描 {root_path} 目录...")
        
        # 创建生成器实例
        generator = ACModGenerator(root_path)
        
        # 发现潜在的模块
        print("\n🔍 发现的潜在模块：")
        modules = generator.discover_modules()
        
        for i, module in enumerate(modules[:5], 1):  # 只显示前5个
            print(f"{i}. {module}")
            
            # 分析模块
            analysis = generator.analyze_module(module)
            
            # 显示分析结果
            print(f"   📁 目录结构: {len(analysis.get('directory_structure', {}).get('children', []))} 个子项")
            print(f"   🔧 核心组件: {len(analysis.get('core_components', []))} 个")
            print(f"   📖 使用示例: {len(analysis.get('usage_examples', []))} 个")
            print(f"   🧪 测试命令: {len(analysis.get('test_commands', []))} 个")
            
            # 检查是否包含 Java 文件
            java_components = [c for c in analysis.get('core_components', []) 
                             if c.get('file', '').endswith('.java')]
            if java_components:
                print(f"   ☕ Java 组件: {len(java_components)} 个")
                for comp in java_components[:3]:  # 显示前3个
                    print(f"      - {comp.get('type', 'unknown')}: {comp.get('name', 'unnamed')}")
            
            print()
        
        return
    
    # 实际的 Java 项目处理
    print(f"📂 分析 Java 项目: {java_project_path}")
    
    # 创建生成器实例
    generator = ACModGenerator(java_project_path.parent)
    
    # 分析指定模块
    analysis = generator.analyze_module(java_project_path)
    
    print("\n📊 分析结果：")
    print(f"模块名称: {analysis.get('module_name', 'Unknown')}")
    print(f"模块描述: {analysis.get('module_description', 'No description')}")
    
    # 显示核心组件
    components = analysis.get('core_components', [])
    if components:
        print(f"\n🔧 核心组件 ({len(components)} 个):")
        for comp in components:
            comp_type = comp.get('type', 'unknown')
            comp_name = comp.get('name', 'unnamed')
            comp_desc = comp.get('description', 'No description')
            print(f"  - {comp_type}: {comp_name}")
            print(f"    {comp_desc}")
            
            # 显示方法（如果是类）
            methods = comp.get('methods', [])
            if methods:
                print(f"    方法: {', '.join(m.get('name', '') for m in methods[:3])}")
                if len(methods) > 3:
                    print(f"    ... 还有 {len(methods) - 3} 个方法")
            print()
    
    # 显示使用示例
    examples = analysis.get('usage_examples', [])
    if examples:
        print(f"📖 使用示例 ({len(examples)} 个):")
        for example in examples:
            file_name = example.get('file', 'unknown')
            language = example.get('language', 'unknown')
            print(f"  - {file_name} ({language})")
        print()
    
    # 显示测试命令
    test_commands = analysis.get('test_commands', [])
    if test_commands:
        print(f"🧪 测试命令:")
        for cmd in test_commands:
            print(f"  - {cmd}")
        print()
    
    # 生成文档
    print("📝 生成 .ac.mod.md 文档...")
    success = generator.generate_documentation(java_project_path, force=True)
    
    if success:
        ac_mod_file = java_project_path / ".ac.mod.md"
        print(f"✅ 文档生成成功: {ac_mod_file}")
        
        # 显示生成的文档的前几行
        if ac_mod_file.exists():
            with open(ac_mod_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                print("\n📄 生成的文档预览:")
                print("-" * 40)
                for line in lines[:15]:  # 显示前15行
                    print(line.rstrip())
                if len(lines) > 15:
                    print("...")
                print("-" * 40)
    else:
        print("❌ 文档生成失败")

def show_java_support_features():
    """显示 Java 支持的功能特性"""
    print("\n☕ Java 项目支持特性：")
    print("=" * 50)
    
    features = [
        "🔍 自动检测 Maven 项目 (pom.xml)",
        "🔍 自动检测 Gradle 项目 (build.gradle, build.gradle.kts)",
        "📁 识别标准 Java 项目结构 (src/main/java, src/test/java)",
        "🔧 解析 Java 类和接口定义",
        "📖 提取 Javadoc 注释作为组件描述",
        "🧪 识别 JUnit 测试文件 (*Test.java)",
        "📝 生成 Maven/Gradle 测试命令",
        "📋 提取使用示例 (Example*.java, Demo*.java)",
        "🏗️ 支持多模块 Java 项目",
        "🔄 与 Python 项目混合支持"
    ]
    
    for feature in features:
        print(f"  {feature}")
    
    print("\n📋 支持的文件类型：")
    print("  - .java (Java 源文件)")
    print("  - pom.xml (Maven 配置)")
    print("  - build.gradle / build.gradle.kts (Gradle 配置)")
    print("  - *Test.java (JUnit 测试文件)")
    print("  - Example*.java, Demo*.java (示例文件)")

if __name__ == "__main__":
    show_java_support_features()
    print()
    generate_java_project_documentation()
