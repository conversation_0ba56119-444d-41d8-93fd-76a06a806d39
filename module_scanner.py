"""
Module Scanner for AC Module Generator

This module provides functionality to scan and analyze code modules
to extract structure, dependencies, and other metadata.
"""

import ast
import re
import os
from pathlib import Path
from typing import List, Dict, Any, Optional, Set
import logging

try:
    from .file_utils import FileUtils
except ImportError:
    from file_utils import FileUtils

logger = logging.getLogger(__name__)


class ModuleScanner:
    """
    Scanner for analyzing code modules and extracting metadata.
    
    Provides functionality to discover modules, analyze their structure,
    extract dependencies, and find usage examples.
    """
    
    def __init__(self, root_path: Path):
        """
        Initialize the module scanner.
        
        Args:
            root_path: Root directory to scan
        """
        self.root_path = root_path
        self.file_utils = FileUtils()
    
    def find_potential_modules(self) -> List[Path]:
        """
        Find directories that could be AC modules.
        
        A potential module is a directory that:
        1. Contains Python files
        2. Has a logical structure (not just random files)
        3. Doesn't already have .ac.mod.md (unless we're updating)
        
        Returns:
            List of potential module directories
        """
        potential_modules = []
        
        # Look for Python packages first
        for directory in self.file_utils.list_directories(self.root_path):
            if self._is_potential_module(directory):
                potential_modules.append(directory)
        
        # Also check the root directory itself
        if self._is_potential_module(self.root_path):
            potential_modules.append(self.root_path)
        
        return potential_modules
    
    def _is_potential_module(self, directory: Path) -> bool:
        """Check if a directory is a potential module."""
        # Skip hidden directories and common non-module directories
        skip_dirs = {'.git', '.vscode', '__pycache__', '.pytest_cache', 'node_modules', 'venv', 'env', 'target', 'build', 'out'}
        if directory.name.startswith('.') or directory.name in skip_dirs:
            return False

        # Look for Python files
        python_files = self.file_utils.find_files_by_extension(directory, ['.py'])
        # Look for Java files
        java_files = self.file_utils.find_files_by_extension(directory, ['.java'])

        # Must have either Python or Java files
        if not python_files and not java_files:
            return False

        # Check if it has some structure
        if python_files:
            # Python module: more than one file or has __init__.py
            if len(python_files) >= 2 or self.file_utils.is_python_package(directory):
                return True

        if java_files:
            # Java module: has Java files and looks like a package structure
            if len(java_files) >= 1 or self._is_java_package(directory):
                return True

        return False

    def _is_java_package(self, directory: Path) -> bool:
        """Check if a directory is a Java package."""
        # Check for common Java project indicators
        java_indicators = [
            'pom.xml',  # Maven
            'build.gradle',  # Gradle
            'build.gradle.kts',  # Gradle Kotlin DSL
            'src/main/java',  # Standard Maven/Gradle structure
        ]

        for indicator in java_indicators:
            if (directory / indicator).exists():
                return True

        # Check if it contains Java files in a package-like structure
        java_files = self.file_utils.find_files_by_extension(directory, ['.java'])
        if java_files:
            # Look for src/main/java structure or direct Java files
            src_main_java = directory / "src" / "main" / "java"
            if src_main_java.exists():
                return True
            # Or if it has multiple Java files, consider it a package
            if len(java_files) >= 2:
                return True

        return False

    def get_directory_structure(self, module_path: Path) -> Dict[str, Any]:
        """
        Get the directory structure of a module.
        
        Args:
            module_path: Path to the module
            
        Returns:
            Dictionary representing the directory tree
        """
        def build_tree(path: Path, max_depth: int = 3, current_depth: int = 0) -> Dict[str, Any]:
            if current_depth >= max_depth:
                return None
            
            node = {
                "name": path.name if path != module_path else f"{path.name}/",
                "description": self._get_file_description(path),
                "children": []
            }
            
            if path.is_dir():
                try:
                    # Sort children: directories first, then files
                    children = sorted(path.iterdir(), key=lambda x: (x.is_file(), x.name))
                    for child in children:
                        if not child.name.startswith('.') and child.name != '__pycache__':
                            child_node = build_tree(child, max_depth, current_depth + 1)
                            if child_node:
                                node["children"].append(child_node)
                except PermissionError:
                    pass
            
            return node
        
        return build_tree(module_path)
    
    def _get_file_description(self, file_path: Path) -> str:
        """Get a description for a file based on its content or name."""
        if file_path.is_dir():
            if file_path.name == "tests":
                return "测试目录"
            elif file_path.name == "docs":
                return "文档目录"
            elif file_path.name == "examples":
                return "示例目录"
            else:
                return "子目录"
        
        # For files, try to extract description from docstring or comments
        if file_path.suffix == '.py':
            try:
                content = self.file_utils.read_file(file_path)
                
                # Try to parse as Python and get module docstring
                try:
                    tree = ast.parse(content)
                    if (tree.body and isinstance(tree.body[0], ast.Expr) 
                        and isinstance(tree.body[0].value, ast.Constant)):
                        docstring = tree.body[0].value.value
                        if isinstance(docstring, str):
                            # Return first line of docstring
                            return docstring.split('\n')[0].strip()
                except:
                    pass
                
                # Look for comments at the top
                lines = content.split('\n')
                for line in lines[:10]:  # Check first 10 lines
                    line = line.strip()
                    if line.startswith('#') and len(line) > 2:
                        comment = line[1:].strip()
                        if len(comment) > 10:  # Meaningful comment
                            return comment
                
            except Exception:
                pass

        # For Java files, extract from comments
        elif file_path.suffix == '.java':
            try:
                content = self.file_utils.read_file(file_path)
                lines = content.split('\n')

                # Look for class-level Javadoc or comments
                in_javadoc = False
                for line in lines:
                    line = line.strip()
                    if line.startswith('/**'):
                        in_javadoc = True
                        continue
                    elif line.startswith('*/'):
                        in_javadoc = False
                        continue
                    elif in_javadoc and line.startswith('*'):
                        comment = line[1:].strip()
                        if len(comment) > 10 and not comment.startswith('@'):
                            return comment
                    elif line.startswith('//') and len(line) > 5:
                        comment = line[2:].strip()
                        if len(comment) > 10:
                            return comment

            except Exception:
                pass

        # Default descriptions based on filename
        name_descriptions = {
            '__init__.py': '模块初始化文件',
            'setup.py': '安装配置文件',
            'requirements.txt': '依赖配置文件',
            'README.md': '项目说明文档',
            '.ac.mod.md': '本文档',
            'pom.xml': 'Maven 项目配置文件',
            'build.gradle': 'Gradle 构建配置文件',
            'build.gradle.kts': 'Gradle Kotlin DSL 配置文件',
            'Application.java': '应用程序主类',
            'Main.java': '程序入口类',
        }
        
        return name_descriptions.get(file_path.name, f"{file_path.suffix[1:].upper()} 文件" if file_path.suffix else "文件")
    
    def extract_core_components(self, module_path: Path) -> List[Dict[str, Any]]:
        """
        Extract core components (classes, functions) from the module.

        Args:
            module_path: Path to the module

        Returns:
            List of component information
        """
        components = []

        # Analyze Python files
        python_files = self.file_utils.find_files_by_extension(module_path, ['.py'])
        for file_path in python_files:
            if file_path.name.startswith('test_') or '/tests/' in str(file_path):
                continue  # Skip test files

            try:
                content = self.file_utils.read_file(file_path)
                file_components = self._analyze_python_file(file_path, content)
                components.extend(file_components)
            except Exception as e:
                logger.warning(f"Failed to analyze Python file {file_path}: {e}")

        # Analyze Java files
        java_files = self.file_utils.find_files_by_extension(module_path, ['.java'])
        for file_path in java_files:
            if 'test' in str(file_path).lower() or file_path.name.endswith('Test.java'):
                continue  # Skip test files

            try:
                content = self.file_utils.read_file(file_path)
                file_components = self._analyze_java_file(file_path, content)
                components.extend(file_components)
            except Exception as e:
                logger.warning(f"Failed to analyze Java file {file_path}: {e}")

        return components

    def _analyze_java_file(self, file_path: Path, content: str) -> List[Dict[str, Any]]:
        """Analyze a Java file and extract components using regex patterns."""
        components = []

        try:
            lines = content.split('\n')
            current_class = None
            current_javadoc = []
            in_javadoc = False

            for line in lines:
                line_stripped = line.strip()

                # Handle Javadoc comments
                if line_stripped.startswith('/**'):
                    in_javadoc = True
                    current_javadoc = []
                    continue
                elif line_stripped.startswith('*/'):
                    in_javadoc = False
                    continue
                elif in_javadoc and line_stripped.startswith('*'):
                    comment = line_stripped[1:].strip()
                    if comment and not comment.startswith('@'):
                        current_javadoc.append(comment)
                    continue

                # Skip empty lines and single-line comments
                if not line_stripped or line_stripped.startswith('//'):
                    continue

                # Match class declarations
                class_match = re.match(r'.*\b(?:public\s+|private\s+|protected\s+)?(?:abstract\s+|final\s+)?class\s+(\w+)', line_stripped)
                if class_match:
                    class_name = class_match.group(1)
                    description = ' '.join(current_javadoc) if current_javadoc else f"Java class {class_name}"

                    current_class = {
                        "name": class_name,
                        "type": "class",
                        "description": description,
                        "methods": [],
                        "file": str(file_path.relative_to(self.root_path))
                    }
                    components.append(current_class)
                    current_javadoc = []
                    continue

                # Match method declarations
                method_match = re.match(r'.*\b(?:public\s+|private\s+|protected\s+)?(?:static\s+)?(?:final\s+)?(?:\w+\s+)+(\w+)\s*\([^)]*\)', line_stripped)
                if method_match and current_class:
                    method_name = method_match.group(1)
                    # Skip constructors and common getters/setters
                    if method_name != current_class["name"] and not method_name.startswith("get") and not method_name.startswith("set"):
                        method_description = ' '.join(current_javadoc) if current_javadoc else f"Method {method_name}"
                        current_class["methods"].append({
                            "name": method_name,
                            "description": method_description
                        })
                    current_javadoc = []

                # Match interface declarations
                interface_match = re.match(r'.*\b(?:public\s+)?interface\s+(\w+)', line_stripped)
                if interface_match:
                    interface_name = interface_match.group(1)
                    description = ' '.join(current_javadoc) if current_javadoc else f"Java interface {interface_name}"

                    components.append({
                        "name": interface_name,
                        "type": "interface",
                        "description": description,
                        "methods": [],
                        "file": str(file_path.relative_to(self.root_path))
                    })
                    current_javadoc = []
                    continue

        except Exception as e:
            logger.warning(f"Error parsing Java file {file_path}: {e}")

        return components

    def _analyze_python_file(self, file_path: Path, content: str) -> List[Dict[str, Any]]:
        """Analyze a Python file and extract components."""
        components = []
        
        try:
            tree = ast.parse(content)
            
            for node in ast.walk(tree):
                if isinstance(node, ast.ClassDef):
                    component = {
                        "name": node.name,
                        "type": "class",
                        "description": self._get_docstring(node),
                        "methods": self._extract_methods(node),
                        "file": str(file_path.relative_to(self.root_path))
                    }
                    components.append(component)
                
                elif isinstance(node, ast.FunctionDef) and not self._is_method(node, tree):
                    component = {
                        "name": node.name,
                        "type": "function",
                        "description": self._get_docstring(node),
                        "file": str(file_path.relative_to(self.root_path))
                    }
                    components.append(component)
        
        except SyntaxError as e:
            logger.warning(f"Syntax error in {file_path}: {e}")
        
        return components
    
    def _get_docstring(self, node) -> str:
        """Extract docstring from an AST node."""
        if (node.body and isinstance(node.body[0], ast.Expr) 
            and isinstance(node.body[0].value, ast.Constant)):
            docstring = node.body[0].value.value
            if isinstance(docstring, str):
                # Return first line of docstring
                return docstring.split('\n')[0].strip()
        return ""
    
    def _extract_methods(self, class_node) -> List[Dict[str, str]]:
        """Extract methods from a class node."""
        methods = []
        for node in class_node.body:
            if isinstance(node, ast.FunctionDef) and not node.name.startswith('_'):
                methods.append({
                    "name": node.name,
                    "description": self._get_docstring(node)
                })
        return methods
    
    def _is_method(self, func_node, tree) -> bool:
        """Check if a function is a method (inside a class)."""
        for node in ast.walk(tree):
            if isinstance(node, ast.ClassDef):
                if func_node in node.body:
                    return True
        return False

    def find_dependencies(self, module_path: Path) -> List[str]:
        """
        Find dependencies on other AC modules.

        Args:
            module_path: Path to the module

        Returns:
            List of relative paths to other .ac.mod.md files
        """
        dependencies = set()

        # Look for other .ac.mod.md files in the project
        ac_mod_files = self.file_utils.list_files(self.root_path, "*.ac.mod.md")

        for ac_mod_file in ac_mod_files:
            if ac_mod_file.parent != module_path:  # Exclude self
                try:
                    rel_path = self.file_utils.get_relative_path(ac_mod_file, module_path)
                    dependencies.add(rel_path)
                except Exception:
                    pass

        return sorted(list(dependencies))

    def find_usage_examples(self, module_path: Path) -> List[Dict[str, str]]:
        """
        Find usage examples for the module.

        Args:
            module_path: Path to the module

        Returns:
            List of usage examples
        """
        examples = []

        # Look for Python example files
        python_example_patterns = ["example*.py", "demo*.py", "*_example.py", "*_demo.py"]
        for pattern in python_example_patterns:
            example_files = self.file_utils.list_files(module_path, pattern)
            for example_file in example_files:
                try:
                    content = self.file_utils.read_file(example_file)
                    examples.append({
                        "file": str(example_file.relative_to(module_path)),
                        "code": content,
                        "language": "python"
                    })
                except Exception:
                    pass

        # Look for Java example files
        java_example_patterns = ["Example*.java", "Demo*.java", "*Example.java", "*Demo.java"]
        for pattern in java_example_patterns:
            example_files = self.file_utils.list_files(module_path, pattern)
            for example_file in example_files:
                try:
                    content = self.file_utils.read_file(example_file)
                    examples.append({
                        "file": str(example_file.relative_to(module_path)),
                        "code": content,
                        "language": "java"
                    })
                except Exception:
                    pass

        # Look for usage in Python test files
        test_files = self.file_utils.list_files(module_path, "test_*.py")
        for test_file in test_files:
            try:
                content = self.file_utils.read_file(test_file)
                # Extract simple usage patterns
                usage_code = self._extract_usage_from_tests(content)
                if usage_code:
                    examples.append({
                        "file": str(test_file.relative_to(module_path)),
                        "code": usage_code,
                        "language": "python"
                    })
            except Exception:
                pass

        # Look for usage in Java test files
        java_test_files = self.file_utils.list_files(module_path, "*Test.java")
        for test_file in java_test_files:
            try:
                content = self.file_utils.read_file(test_file)
                # Extract simple usage patterns from Java tests
                usage_code = self._extract_usage_from_java_tests(content)
                if usage_code:
                    examples.append({
                        "file": str(test_file.relative_to(module_path)),
                        "code": usage_code,
                        "language": "java"
                    })
            except Exception:
                pass

        return examples

    def _extract_usage_from_tests(self, test_content: str) -> str:
        """Extract usage examples from test files."""
        # This is a simplified extraction - could be more sophisticated
        lines = test_content.split('\n')
        usage_lines = []

        in_test_function = False
        for line in lines:
            stripped = line.strip()

            # Start of test function
            if stripped.startswith('def test_'):
                in_test_function = True
                continue

            # End of function
            if in_test_function and stripped.startswith('def '):
                break

            # Collect meaningful lines from test functions
            if in_test_function and stripped and not stripped.startswith('#'):
                # Skip assert statements for cleaner examples
                if not stripped.startswith('assert'):
                    usage_lines.append(line)

        return '\n'.join(usage_lines[:20])  # Limit to first 20 lines

    def _extract_usage_from_java_tests(self, test_content: str) -> str:
        """Extract usage examples from Java test files."""
        lines = test_content.split('\n')
        usage_lines = []

        in_test_method = False
        brace_count = 0

        for line in lines:
            stripped = line.strip()

            # Start of test method (look for @Test annotation or test method name)
            if '@Test' in stripped or (stripped.startswith('public') and 'test' in stripped.lower()):
                in_test_method = True
                continue

            if in_test_method:
                # Count braces to track method boundaries
                brace_count += stripped.count('{') - stripped.count('}')

                # End of method
                if brace_count <= 0 and '}' in stripped:
                    break

                # Collect meaningful lines from test methods
                if stripped and not stripped.startswith('//') and not stripped.startswith('/*'):
                    # Skip assert statements for cleaner examples
                    if not stripped.startswith('assert') and not stripped.startswith('Assert.'):
                        usage_lines.append(line)

        return '\n'.join(usage_lines[:20])  # Limit to first 20 lines

    def find_test_commands(self, module_path: Path) -> List[str]:
        """
        Find test commands for the module.

        Args:
            module_path: Path to the module

        Returns:
            List of test commands
        """
        commands = []

        # Check for Python tests
        if self.file_utils.list_files(module_path, "test_*.py"):
            rel_path = self.file_utils.get_relative_path(module_path, self.root_path)
            commands.append(f"pytest {rel_path} -v")

        # Check for Java tests with Maven
        if (module_path / "pom.xml").exists():
            commands.append("mvn test")
            commands.append("mvn clean test")

        # Check for Java tests with Gradle
        if (module_path / "build.gradle").exists() or (module_path / "build.gradle.kts").exists():
            commands.append("./gradlew test")
            commands.append("gradle test")

        # Check for Java test files
        java_test_files = self.file_utils.list_files(module_path, "*Test.java")
        if java_test_files:
            # If no build tool found, suggest basic javac/java commands
            if not any(cmd.startswith(('mvn', './gradlew', 'gradle')) for cmd in commands):
                commands.append("# Compile and run tests manually:")
                commands.append("javac -cp .:junit.jar *.java")
                commands.append("java -cp .:junit.jar org.junit.runner.JUnitCore TestClassName")

        # Check for setup.py
        if (module_path / "setup.py").exists():
            commands.append("python setup.py test")

        # Check for Makefile
        makefile_path = module_path / "Makefile"
        if makefile_path.exists():
            try:
                content = self.file_utils.read_file(makefile_path)
                if "test:" in content:
                    commands.append("make test")
            except Exception:
                pass

        # Check for package.json (for mixed projects)
        package_json = module_path / "package.json"
        if package_json.exists():
            commands.append("npm test")

        return commands

    def extract_module_description(self, module_path: Path) -> str:
        """
        Extract module description from various sources.

        Args:
            module_path: Path to the module

        Returns:
            Module description string
        """
        # Try README files first
        readme_files = ["README.md", "README.rst", "README.txt", "readme.md"]
        for readme_name in readme_files:
            readme_path = module_path / readme_name
            if readme_path.exists():
                try:
                    content = self.file_utils.read_file(readme_path)
                    # Extract first meaningful line
                    lines = content.split('\n')
                    for line in lines:
                        line = line.strip()
                        if line and not line.startswith('#') and len(line) > 10:
                            return line
                except Exception:
                    pass

        # Try __init__.py docstring
        init_file = module_path / "__init__.py"
        if init_file.exists():
            try:
                content = self.file_utils.read_file(init_file)
                tree = ast.parse(content)
                if (tree.body and isinstance(tree.body[0], ast.Expr)
                    and isinstance(tree.body[0].value, ast.Constant)):
                    docstring = tree.body[0].value.value
                    if isinstance(docstring, str):
                        return docstring.split('\n')[0].strip()
            except Exception:
                pass

        # Default description
        return f"{module_path.name} 模块的核心功能组件"
