#!/usr/bin/env python3
"""
Test script to verify Java project support in AC Module Generator
"""

import tempfile
import shutil
from pathlib import Path
import sys
sys.path.append('.')
from module_scanner import ModuleScanner
from file_utils import FileUtils

def create_test_java_project():
    """Create a temporary Java project for testing."""
    temp_dir = Path(tempfile.mkdtemp())
    
    # Create Maven project structure
    java_dir = temp_dir / "src" / "main" / "java" / "com" / "example"
    java_dir.mkdir(parents=True)
    
    test_dir = temp_dir / "src" / "test" / "java" / "com" / "example"
    test_dir.mkdir(parents=True)
    
    # Create pom.xml
    pom_content = """<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.example</groupId>
    <artifactId>test-module</artifactId>
    <version>1.0.0</version>
    <packaging>jar</packaging>
    
    <properties>
        <maven.compiler.source>11</maven.compiler.source>
        <maven.compiler.target>11</maven.compiler.target>
    </properties>
    
    <dependencies>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>4.13.2</version>
            <scope>test</scope>
        </dependency>
    </dependencies>
</project>"""
    
    FileUtils.write_file(temp_dir / "pom.xml", pom_content)
    
    # Create main Java class
    main_class_content = """package com.example;

/**
 * Main application class for the test module.
 * This class demonstrates the core functionality.
 */
public class Application {
    
    private String name;
    
    /**
     * Constructor for Application.
     * @param name The application name
     */
    public Application(String name) {
        this.name = name;
    }
    
    /**
     * Process data using the application logic.
     * @param data Input data to process
     * @return Processed result
     */
    public String processData(String data) {
        return "Processed: " + data + " by " + name;
    }
    
    /**
     * Get the application name.
     * @return The application name
     */
    public String getName() {
        return name;
    }
    
    /**
     * Main entry point.
     * @param args Command line arguments
     */
    public static void main(String[] args) {
        Application app = new Application("TestApp");
        System.out.println(app.processData("sample data"));
    }
}"""
    
    FileUtils.write_file(java_dir / "Application.java", main_class_content)
    
    # Create utility class
    util_class_content = """package com.example;

/**
 * Utility class providing helper methods.
 */
public class Utils {
    
    /**
     * Format a string with proper capitalization.
     * @param input Input string
     * @return Formatted string
     */
    public static String formatString(String input) {
        if (input == null || input.isEmpty()) {
            return input;
        }
        return input.substring(0, 1).toUpperCase() + input.substring(1).toLowerCase();
    }
    
    /**
     * Check if a string is valid.
     * @param value String to validate
     * @return true if valid, false otherwise
     */
    public static boolean isValid(String value) {
        return value != null && !value.trim().isEmpty();
    }
}"""
    
    FileUtils.write_file(java_dir / "Utils.java", util_class_content)
    
    # Create test class
    test_class_content = """package com.example;

import org.junit.Test;
import static org.junit.Assert.*;

/**
 * Test class for Application functionality.
 */
public class ApplicationTest {
    
    @Test
    public void testProcessData() {
        Application app = new Application("TestApp");
        String result = app.processData("test");
        assertEquals("Processed: test by TestApp", result);
    }
    
    @Test
    public void testGetName() {
        Application app = new Application("MyApp");
        assertEquals("MyApp", app.getName());
    }
}"""
    
    FileUtils.write_file(test_dir / "ApplicationTest.java", test_class_content)
    
    # Create example class
    example_content = """package com.example;

/**
 * Example demonstrating how to use the Application class.
 */
public class ExampleUsage {
    
    public static void main(String[] args) {
        // Create application instance
        Application app = new Application("ExampleApp");
        
        // Process some data
        String result = app.processData("example data");
        System.out.println("Result: " + result);
        
        // Use utility methods
        String formatted = Utils.formatString("hello world");
        System.out.println("Formatted: " + formatted);
    }
}"""
    
    FileUtils.write_file(java_dir / "ExampleUsage.java", example_content)
    
    return temp_dir

def test_java_project_detection():
    """Test that Java projects are properly detected."""
    print("Testing Java project detection...")
    
    temp_dir = create_test_java_project()
    try:
        scanner = ModuleScanner(temp_dir)
        
        # Test module detection
        modules = scanner.find_potential_modules()
        print(f"Found {len(modules)} potential modules: {modules}")
        assert len(modules) > 0, "Should detect at least one module"
        
        # Test Java project detection
        file_utils = FileUtils()
        is_java = file_utils.is_java_project(temp_dir)
        print(f"Is Java project: {is_java}")
        assert is_java, "Should detect as Java project"
        
        # Test component extraction
        components = scanner.extract_core_components(temp_dir)
        print(f"Found {len(components)} components:")
        for comp in components:
            print(f"  - {comp['type']}: {comp['name']} ({comp.get('description', 'No description')})")
        
        assert len(components) > 0, "Should extract components from Java files"
        
        # Check for specific classes
        class_names = [comp['name'] for comp in components if comp['type'] == 'class']
        assert 'Application' in class_names, "Should find Application class"
        assert 'Utils' in class_names, "Should find Utils class"
        
        # Test usage examples
        examples = scanner.find_usage_examples(temp_dir)
        print(f"Found {len(examples)} usage examples:")
        for example in examples:
            print(f"  - {example['file']} ({example['language']})")
        
        # Test test commands
        test_commands = scanner.find_test_commands(temp_dir)
        print(f"Found test commands: {test_commands}")
        assert any('mvn' in cmd for cmd in test_commands), "Should suggest Maven test commands"
        
        print("✓ Java project detection test passed!")
        
    finally:
        # Cleanup
        shutil.rmtree(temp_dir)

if __name__ == "__main__":
    test_java_project_detection()
    print("\n🎉 All Java support tests passed!")
