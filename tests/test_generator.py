"""
Tests for ACModGenerator class
"""

import pytest
import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock, patch

from ..generator import ACModGenerator
from ..module_scanner import ModuleScanner
from ..template_engine import TemplateEngine


class TestACModGenerator:
    """Test cases for ACModGenerator class."""
    
    def setup_method(self):
        """Setup test environment."""
        self.temp_dir = Path(tempfile.mkdtemp())
        self.generator = ACModGenerator(self.temp_dir)
    
    def teardown_method(self):
        """Cleanup test environment."""
        if self.temp_dir.exists():
            shutil.rmtree(self.temp_dir)
    
    def test_init(self):
        """Test generator initialization."""
        assert self.generator.root_path == self.temp_dir
        assert isinstance(self.generator.scanner, ModuleScanner)
        assert isinstance(self.generator.template_engine, TemplateEngine)
    
    def test_discover_modules_empty_directory(self):
        """Test module discovery in empty directory."""
        modules = self.generator.discover_modules()
        assert modules == []
    
    def test_discover_modules_with_python_package(self):
        """Test module discovery with Python package."""
        # Create a Python package
        package_dir = self.temp_dir / "test_package"
        package_dir.mkdir()
        (package_dir / "__init__.py").write_text("# Test package")
        (package_dir / "module.py").write_text("def test_function(): pass")
        
        modules = self.generator.discover_modules()
        assert len(modules) >= 1
        assert package_dir in modules
    
    def test_analyze_module(self):
        """Test module analysis."""
        # Create a test module
        module_dir = self.temp_dir / "test_module"
        module_dir.mkdir()
        
        # Create __init__.py with docstring
        init_content = '''"""Test module for analysis."""
class TestClass:
    """A test class."""
    def test_method(self):
        """A test method."""
        pass

def test_function():
    """A test function."""
    pass
'''
        (module_dir / "__init__.py").write_text(init_content)
        
        analysis = self.generator.analyze_module(module_dir)
        
        assert analysis["name"] == "test_module"
        assert "Test module for analysis" in analysis["description"]
        assert "directory_structure" in analysis
        assert "core_components" in analysis
        assert len(analysis["core_components"]) >= 1
    
    def test_generate_documentation_new_file(self):
        """Test generating new documentation file."""
        # Create a test module
        module_dir = self.temp_dir / "test_module"
        module_dir.mkdir()
        (module_dir / "__init__.py").write_text("# Test module")
        
        success = self.generator.generate_documentation(module_dir)
        
        assert success is True
        doc_path = module_dir / ".ac.mod.md"
        assert doc_path.exists()
        
        content = doc_path.read_text()
        assert "test_module" in content
        assert "## 目录结构" in content
    
    def test_generate_documentation_existing_file_no_force(self):
        """Test generating documentation when file exists without force."""
        # Create a test module with existing documentation
        module_dir = self.temp_dir / "test_module"
        module_dir.mkdir()
        (module_dir / "__init__.py").write_text("# Test module")
        
        doc_path = module_dir / ".ac.mod.md"
        doc_path.write_text("# Existing documentation")
        
        success = self.generator.generate_documentation(module_dir, force=False)
        
        assert success is False
        assert doc_path.read_text() == "# Existing documentation"
    
    def test_generate_documentation_existing_file_with_force(self):
        """Test generating documentation when file exists with force."""
        # Create a test module with existing documentation
        module_dir = self.temp_dir / "test_module"
        module_dir.mkdir()
        (module_dir / "__init__.py").write_text("# Test module")
        
        doc_path = module_dir / ".ac.mod.md"
        doc_path.write_text("# Existing documentation")
        
        success = self.generator.generate_documentation(module_dir, force=True)
        
        assert success is True
        content = doc_path.read_text()
        assert "test_module" in content
        assert content != "# Existing documentation"

    def test_generate_hierarchical_documentation(self):
        """Test hierarchical documentation generation."""
        # Create a Java module with subpackages
        java_module = self.temp_dir / "java_module"
        java_module.mkdir()
        (java_module / "pom.xml").write_text("<project></project>")

        src_main_java = java_module / "src" / "main" / "java" / "com" / "example"
        src_main_java.mkdir(parents=True)

        # Create main module files
        (src_main_java / "Application.java").write_text("public class Application {}")

        # Create controller subpackage
        controller_pkg = src_main_java / "controller"
        controller_pkg.mkdir()
        (controller_pkg / "UserController.java").write_text("public class UserController {}")
        (controller_pkg / "OrderController.java").write_text("public class OrderController {}")
        (controller_pkg / "ProductController.java").write_text("public class ProductController {}")

        # Create service subpackage
        service_pkg = src_main_java / "service"
        service_pkg.mkdir()
        (service_pkg / "UserService.java").write_text("public class UserService {}")

        # Generate hierarchical documentation
        results = self.generator.generate_hierarchical_documentation(java_module, force=True)

        # Verify results
        assert len(results) >= 3  # Main module + at least 2 subpackages
        assert str(java_module) in results
        assert results[str(java_module)] == True  # Main module should succeed

        # Check that subpackage documentation was generated
        controller_doc = controller_pkg / ".ac.mod.md"
        service_doc = service_pkg / ".ac.mod.md"

        assert controller_doc.exists()
        assert service_doc.exists()

        # Verify content contains expected information
        controller_content = self.file_utils.read_file(controller_doc)
        assert "controller" in controller_content.lower()
        assert "UserController" in controller_content
    
    def test_generate_all_documentation(self):
        """Test generating documentation for all modules."""
        # Create multiple test modules
        for i in range(3):
            module_dir = self.temp_dir / f"test_module_{i}"
            module_dir.mkdir()
            (module_dir / "__init__.py").write_text(f"# Test module {i}")
            (module_dir / "main.py").write_text(f"def main_{i}(): pass")
        
        results = self.generator.generate_all_documentation()
        
        # Should have results for discovered modules
        assert len(results) >= 3
        
        # Check that documentation files were created
        for i in range(3):
            module_dir = self.temp_dir / f"test_module_{i}"
            doc_path = module_dir / ".ac.mod.md"
            if str(module_dir) in results and results[str(module_dir)]:
                assert doc_path.exists()
    
    def test_update_existing_documentation(self):
        """Test updating existing documentation."""
        # Create a test module with existing documentation
        module_dir = self.temp_dir / "test_module"
        module_dir.mkdir()
        (module_dir / "__init__.py").write_text("# Test module")
        
        doc_path = module_dir / ".ac.mod.md"
        original_content = """# test_module

Original description

## 目录结构

```
old structure
```

## 快速开始

Original quick start
"""
        doc_path.write_text(original_content)
        
        success = self.generator.update_existing_documentation(module_dir)
        
        assert success is True
        updated_content = doc_path.read_text()
        assert "test_module" in updated_content
        # Should preserve some original content while updating structure
        assert "## 目录结构" in updated_content

    def test_generate_all_hierarchical_documentation(self):
        """Test generating hierarchical documentation for all modules."""
        # Create multiple Java modules
        java_module1 = self.temp_dir / "module1"
        java_module1.mkdir()
        (java_module1 / "pom.xml").write_text("<project></project>")

        src1 = java_module1 / "src" / "main" / "java" / "com" / "example1"
        src1.mkdir(parents=True)
        (src1 / "App1.java").write_text("public class App1 {}")

        controller1 = src1 / "controller"
        controller1.mkdir()
        (controller1 / "Controller1.java").write_text("public class Controller1 {}")
        (controller1 / "Controller2.java").write_text("public class Controller2 {}")
        (controller1 / "Controller3.java").write_text("public class Controller3 {}")

        java_module2 = self.temp_dir / "module2"
        java_module2.mkdir()
        (java_module2 / "pom.xml").write_text("<project></project>")

        src2 = java_module2 / "src" / "main" / "java" / "com" / "example2"
        src2.mkdir(parents=True)
        (src2 / "App2.java").write_text("public class App2 {}")

        service2 = src2 / "service"
        service2.mkdir()
        (service2 / "Service1.java").write_text("public class Service1 {}")

        # Generate hierarchical documentation for all
        all_results = self.generator.generate_all_hierarchical_documentation(force=True)

        # Verify results
        assert len(all_results) == 2  # Two main modules
        assert str(java_module1) in all_results
        assert str(java_module2) in all_results

        # Check that each module has its own results
        module1_results = all_results[str(java_module1)]
        module2_results = all_results[str(java_module2)]

        assert len(module1_results) >= 2  # Main + controller
        assert len(module2_results) >= 2  # Main + service

        # Verify documentation files exist
        assert (java_module1 / ".ac.mod.md").exists()
        assert (java_module2 / ".ac.mod.md").exists()
        assert (controller1 / ".ac.mod.md").exists()
        assert (service2 / ".ac.mod.md").exists()

    def test_hierarchical_documentation_non_java_module(self):
        """Test hierarchical documentation on non-Java module."""
        # Create a Python module
        python_module = self.temp_dir / "python_module"
        python_module.mkdir()
        (python_module / "__init__.py").write_text("")
        (python_module / "main.py").write_text("def main(): pass")

        # Generate hierarchical documentation
        results = self.generator.generate_hierarchical_documentation(python_module, force=True)

        # Should only generate documentation for the main module
        assert len(results) == 1
        assert str(python_module) in results
        assert results[str(python_module)] == True

        # Verify documentation exists
        doc_path = python_module / ".ac.mod.md"
        assert doc_path.exists()
    
    def test_update_nonexistent_documentation(self):
        """Test updating documentation when file doesn't exist."""
        # Create a test module without documentation
        module_dir = self.temp_dir / "test_module"
        module_dir.mkdir()
        (module_dir / "__init__.py").write_text("# Test module")
        
        success = self.generator.update_existing_documentation(module_dir)
        
        assert success is True
        doc_path = module_dir / ".ac.mod.md"
        assert doc_path.exists()
    
    @patch('logging.Logger.error')
    def test_generate_documentation_error_handling(self, mock_logger):
        """Test error handling in documentation generation."""
        # Try to generate documentation for non-existent module
        non_existent = self.temp_dir / "non_existent"
        
        success = self.generator.generate_documentation(non_existent)
        
        assert success is False
        mock_logger.assert_called()
    
    def test_custom_template_path(self):
        """Test generator with custom template path."""
        template_path = self.temp_dir / "custom_template.md"
        template_content = "# {{MODULE_NAME}}\nCustom template content"
        template_path.write_text(template_content)
        
        generator = ACModGenerator(self.temp_dir, str(template_path))
        
        # Create a test module
        module_dir = self.temp_dir / "test_module"
        module_dir.mkdir()
        (module_dir / "__init__.py").write_text("# Test module")
        
        success = generator.generate_documentation(module_dir)
        
        assert success is True
        doc_path = module_dir / ".ac.mod.md"
        content = doc_path.read_text()
        assert "Custom template content" in content
