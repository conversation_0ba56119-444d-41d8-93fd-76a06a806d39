"""
Tests for ModuleScanner class
"""

import pytest
import tempfile
import shutil
from pathlib import Path

from ..module_scanner import ModuleScanner


class TestModuleScanner:
    """Test cases for ModuleScanner class."""
    
    def setup_method(self):
        """Setup test environment."""
        self.temp_dir = Path(tempfile.mkdtemp())
        self.scanner = ModuleScanner(self.temp_dir)
    
    def teardown_method(self):
        """Cleanup test environment."""
        if self.temp_dir.exists():
            shutil.rmtree(self.temp_dir)
    
    def test_init(self):
        """Test scanner initialization."""
        assert self.scanner.root_path == self.temp_dir
    
    def test_find_potential_modules_empty(self):
        """Test finding modules in empty directory."""
        modules = self.scanner.find_potential_modules()
        assert modules == []
    
    def test_find_potential_modules_with_python_package(self):
        """Test finding Python package modules."""
        # Create a Python package
        package_dir = self.temp_dir / "test_package"
        package_dir.mkdir()
        (package_dir / "__init__.py").write_text("# Package init")
        (package_dir / "module.py").write_text("def function(): pass")
        
        modules = self.scanner.find_potential_modules()
        assert package_dir in modules
    
    def test_find_potential_modules_with_multiple_python_files(self):
        """Test finding modules with multiple Python files."""
        # Create directory with multiple Python files
        module_dir = self.temp_dir / "test_module"
        module_dir.mkdir()
        (module_dir / "file1.py").write_text("def func1(): pass")
        (module_dir / "file2.py").write_text("def func2(): pass")
        
        modules = self.scanner.find_potential_modules()
        assert module_dir in modules
    
    def test_skip_hidden_directories(self):
        """Test that hidden directories are skipped."""
        # Create hidden directory
        hidden_dir = self.temp_dir / ".hidden"
        hidden_dir.mkdir()
        (hidden_dir / "file.py").write_text("def func(): pass")
        
        modules = self.scanner.find_potential_modules()
        assert hidden_dir not in modules
    
    def test_skip_common_non_module_directories(self):
        """Test that common non-module directories are skipped."""
        skip_dirs = ['__pycache__', '.git', 'node_modules', 'venv']
        
        for dir_name in skip_dirs:
            dir_path = self.temp_dir / dir_name
            dir_path.mkdir()
            (dir_path / "file.py").write_text("def func(): pass")
        
        modules = self.scanner.find_potential_modules()
        for dir_name in skip_dirs:
            dir_path = self.temp_dir / dir_name
            assert dir_path not in modules
    
    def test_get_directory_structure(self):
        """Test getting directory structure."""
        # Create a module with nested structure
        module_dir = self.temp_dir / "test_module"
        module_dir.mkdir()
        (module_dir / "__init__.py").write_text("# Init file")
        (module_dir / "main.py").write_text("def main(): pass")
        
        subdir = module_dir / "submodule"
        subdir.mkdir()
        (subdir / "sub.py").write_text("def sub_func(): pass")
        
        structure = self.scanner.get_directory_structure(module_dir)
        
        assert structure["name"] == "test_module/"
        assert len(structure["children"]) >= 2  # At least __init__.py and main.py
        
        # Check for submodule
        child_names = [child["name"] for child in structure["children"]]
        assert "submodule" in child_names

    def test_is_java_subpackage(self):
        """Test Java subpackage identification."""
        # Create a Java module structure
        java_module = self.temp_dir / "java_module"
        java_module.mkdir()
        (java_module / "pom.xml").write_text("<project></project>")

        src_main_java = java_module / "src" / "main" / "java" / "com" / "example"
        src_main_java.mkdir(parents=True)

        # Create a controller package with multiple files
        controller_pkg = src_main_java / "controller"
        controller_pkg.mkdir()
        (controller_pkg / "UserController.java").write_text("public class UserController {}")
        (controller_pkg / "OrderController.java").write_text("public class OrderController {}")
        (controller_pkg / "ProductController.java").write_text("public class ProductController {}")

        # Create a service package with fewer files but functional name
        service_pkg = src_main_java / "service"
        service_pkg.mkdir()
        (service_pkg / "UserService.java").write_text("public class UserService {}")

        # Create a util package with interface
        util_pkg = src_main_java / "util"
        util_pkg.mkdir()
        (util_pkg / "Helper.java").write_text("public interface Helper {}")

        # Create a test package (should be ignored)
        test_pkg = src_main_java / "test"
        test_pkg.mkdir()
        (test_pkg / "TestHelper.java").write_text("public class TestHelper {}")

        # Test subpackage identification
        assert self.scanner._is_java_subpackage(controller_pkg)  # Multiple files
        assert self.scanner._is_java_subpackage(service_pkg)     # Functional name
        assert self.scanner._is_java_subpackage(util_pkg)       # Contains interface
        assert not self.scanner._is_java_subpackage(test_pkg)   # Test package

    def test_find_java_subpackages(self):
        """Test finding Java subpackages."""
        # Create a Java module structure
        java_module = self.temp_dir / "java_module"
        java_module.mkdir()
        (java_module / "pom.xml").write_text("<project></project>")

        src_main_java = java_module / "src" / "main" / "java" / "com" / "example"
        src_main_java.mkdir(parents=True)

        # Create meaningful subpackages
        controller_pkg = src_main_java / "controller"
        controller_pkg.mkdir()
        (controller_pkg / "UserController.java").write_text("public class UserController {}")
        (controller_pkg / "OrderController.java").write_text("public class OrderController {}")
        (controller_pkg / "ProductController.java").write_text("public class ProductController {}")

        service_pkg = src_main_java / "service"
        service_pkg.mkdir()
        (service_pkg / "UserService.java").write_text("public class UserService {}")

        # Create a package that shouldn't be included
        empty_pkg = src_main_java / "empty"
        empty_pkg.mkdir()

        subpackages = self.scanner._find_java_subpackages(java_module)
        subpackage_names = [pkg.name for pkg in subpackages]

        assert "controller" in subpackage_names
        assert "service" in subpackage_names
        assert "empty" not in subpackage_names

    def test_find_potential_modules_with_subpackages(self):
        """Test finding potential modules including subpackages."""
        # Create a Java module structure
        java_module = self.temp_dir / "java_module"
        java_module.mkdir()
        (java_module / "pom.xml").write_text("<project></project>")

        src_main_java = java_module / "src" / "main" / "java" / "com" / "example"
        src_main_java.mkdir(parents=True)

        # Create subpackages
        controller_pkg = src_main_java / "controller"
        controller_pkg.mkdir()
        (controller_pkg / "UserController.java").write_text("public class UserController {}")
        (controller_pkg / "OrderController.java").write_text("public class OrderController {}")
        (controller_pkg / "ProductController.java").write_text("public class ProductController {}")

        service_pkg = src_main_java / "service"
        service_pkg.mkdir()
        (service_pkg / "UserService.java").write_text("public class UserService {}")

        # Test without subpackages
        modules_without_sub = self.scanner.find_potential_modules(include_subpackages=False)
        module_names_without_sub = [m.name for m in modules_without_sub]
        assert "java_module" in module_names_without_sub
        assert "controller" not in module_names_without_sub
        assert "service" not in module_names_without_sub

        # Test with subpackages
        modules_with_sub = self.scanner.find_potential_modules(include_subpackages=True)
        all_paths = [str(m) for m in modules_with_sub]

        # Should include main module and subpackages
        assert any("java_module" in path and "controller" not in path for path in all_paths)
        assert any("controller" in path for path in all_paths)
        assert any("service" in path for path in all_paths)
    
    def test_extract_core_components(self):
        """Test extracting core components from module."""
        # Create a module with classes and functions
        module_dir = self.temp_dir / "test_module"
        module_dir.mkdir()
        
        code_content = '''
"""Module docstring."""

class TestClass:
    """A test class."""
    
    def method1(self):
        """Method 1."""
        pass
    
    def method2(self):
        """Method 2."""
        pass

def standalone_function():
    """A standalone function."""
    pass

def _private_function():
    """A private function."""
    pass
'''
        (module_dir / "main.py").write_text(code_content)
        
        components = self.scanner.extract_core_components(module_dir)
        
        # Should find the class and standalone function
        assert len(components) >= 2
        
        # Check class component
        class_component = next((c for c in components if c["name"] == "TestClass"), None)
        assert class_component is not None
        assert class_component["type"] == "class"
        assert len(class_component["methods"]) == 2
        
        # Check function component
        func_component = next((c for c in components if c["name"] == "standalone_function"), None)
        assert func_component is not None
        assert func_component["type"] == "function"
    
    def test_find_dependencies(self):
        """Test finding dependencies on other AC modules."""
        # Create main module
        module_dir = self.temp_dir / "main_module"
        module_dir.mkdir()
        (module_dir / "__init__.py").write_text("# Main module")
        
        # Create dependency modules with .ac.mod.md files
        dep1_dir = self.temp_dir / "dep1"
        dep1_dir.mkdir()
        (dep1_dir / ".ac.mod.md").write_text("# Dependency 1")
        
        dep2_dir = self.temp_dir / "subdir" / "dep2"
        dep2_dir.mkdir(parents=True)
        (dep2_dir / ".ac.mod.md").write_text("# Dependency 2")
        
        dependencies = self.scanner.find_dependencies(module_dir)
        
        # Should find relative paths to other .ac.mod.md files
        assert len(dependencies) >= 2
        assert any("dep1" in dep for dep in dependencies)
        assert any("dep2" in dep for dep in dependencies)
    
    def test_find_usage_examples(self):
        """Test finding usage examples."""
        module_dir = self.temp_dir / "test_module"
        module_dir.mkdir()
        
        # Create example file
        example_content = '''
# Example usage
from test_module import TestClass

def main():
    obj = TestClass()
    obj.do_something()

if __name__ == "__main__":
    main()
'''
        (module_dir / "example_usage.py").write_text(example_content)
        
        # Create test file
        test_content = '''
import unittest
from test_module import TestClass

class TestTestClass(unittest.TestCase):
    def test_creation(self):
        obj = TestClass()
        self.assertIsNotNone(obj)
    
    def test_method(self):
        obj = TestClass()
        result = obj.do_something()
        self.assertTrue(result)
'''
        (module_dir / "test_module.py").write_text(test_content)
        
        examples = self.scanner.find_usage_examples(module_dir)
        
        assert len(examples) >= 1
        # Should find the example file
        example_files = [ex["file"] for ex in examples]
        assert any("example" in f for f in example_files)
    
    def test_find_test_commands(self):
        """Test finding test commands."""
        module_dir = self.temp_dir / "test_module"
        module_dir.mkdir()
        
        # Create test file
        (module_dir / "test_something.py").write_text("def test_func(): pass")
        
        # Create Makefile with test target
        makefile_content = '''
.PHONY: test
test:
\tpytest tests/

build:
\tpython setup.py build
'''
        (module_dir / "Makefile").write_text(makefile_content)
        
        commands = self.scanner.find_test_commands(module_dir)
        
        # Should find pytest command and make test
        assert len(commands) >= 1
        assert any("pytest" in cmd for cmd in commands)
        assert any("make test" in cmd for cmd in commands)
    
    def test_extract_module_description_from_readme(self):
        """Test extracting description from README."""
        module_dir = self.temp_dir / "test_module"
        module_dir.mkdir()
        
        readme_content = '''# Test Module

This is a test module for demonstration purposes.

## Installation

pip install test-module
'''
        (module_dir / "README.md").write_text(readme_content)
        
        description = self.scanner.extract_module_description(module_dir)
        
        assert "test module for demonstration purposes" in description.lower()
    
    def test_extract_module_description_from_init(self):
        """Test extracting description from __init__.py."""
        module_dir = self.temp_dir / "test_module"
        module_dir.mkdir()
        
        init_content = '''"""
This is a comprehensive test module.

It provides various testing utilities.
"""

from .main import TestClass
'''
        (module_dir / "__init__.py").write_text(init_content)
        
        description = self.scanner.extract_module_description(module_dir)
        
        assert "comprehensive test module" in description.lower()
    
    def test_extract_module_description_default(self):
        """Test default description when no sources available."""
        module_dir = self.temp_dir / "test_module"
        module_dir.mkdir()
        
        description = self.scanner.extract_module_description(module_dir)
        
        assert "test_module" in description
        assert "模块的核心功能组件" in description
