"""
Tests for TemplateEngine class
"""

import pytest
import tempfile
import shutil
from pathlib import Path

from ..template_engine import TemplateEngine


class TestTemplateEngine:
    """Test cases for TemplateEngine class."""
    
    def setup_method(self):
        """Setup test environment."""
        self.temp_dir = Path(tempfile.mkdtemp())
        self.engine = TemplateEngine()
    
    def teardown_method(self):
        """Cleanup test environment."""
        if self.temp_dir.exists():
            shutil.rmtree(self.temp_dir)
    
    def test_init_default(self):
        """Test engine initialization with default template."""
        engine = TemplateEngine()
        assert engine.template_path is None
        assert "{{MODULE_NAME}}" in engine.default_template
    
    def test_init_custom_template(self):
        """Test engine initialization with custom template."""
        template_path = str(self.temp_dir / "custom.md")
        engine = TemplateEngine(template_path)
        assert engine.template_path == template_path
    
    def test_render_basic_analysis(self):
        """Test rendering with basic analysis data."""
        analysis = {
            "name": "test_module",
            "description": "A test module for testing",
            "path": "src/test_module",
            "directory_structure": {
                "name": "test_module/",
                "children": [
                    {"name": "__init__.py", "description": "Module init"},
                    {"name": "main.py", "description": "Main module"}
                ]
            },
            "core_components": [
                {
                    "name": "TestClass",
                    "type": "class",
                    "description": "A test class",
                    "methods": [
                        {"name": "test_method", "description": "A test method"}
                    ]
                }
            ],
            "dependencies": ["../other_module/.ac.mod.md"],
            "usage_examples": [
                {"code": "from test_module import TestClass\nobj = TestClass()", "language": "python"}
            ],
            "test_commands": ["pytest src/test_module -v"]
        }
        
        content = self.engine.render(analysis)
        
        assert "# test_module" in content
        assert "A test module for testing" in content
        assert "## 目录结构" in content
        assert "test_module/" in content
        assert "__init__.py" in content
        assert "## 核心组件详解" in content
        assert "TestClass" in content
        assert "## 依赖关系说明" in content
        assert "../other_module/.ac.mod.md" in content
        assert "pytest src/test_module -v" in content
    
    def test_render_with_custom_template(self):
        """Test rendering with custom template file."""
        # Create custom template
        template_path = self.temp_dir / "custom.md"
        template_content = """# {{MODULE_NAME}} Custom
        
Custom description: {{MODULE_DESCRIPTION}}

## Custom Structure
{{DIRECTORY_STRUCTURE}}

## Custom Components
{{CORE_COMPONENTS}}
"""
        template_path.write_text(template_content)
        
        engine = TemplateEngine(str(template_path))
        
        analysis = {
            "name": "test_module",
            "description": "Test description",
            "directory_structure": {"name": "test/", "children": []},
            "core_components": [],
            "dependencies": [],
            "usage_examples": [],
            "test_commands": []
        }
        
        content = engine.render(analysis)
        
        assert "# test_module Custom" in content
        assert "Custom description: Test description" in content
        assert "## Custom Structure" in content
        assert "## Custom Components" in content
    
    def test_render_directory_structure_empty(self):
        """Test rendering empty directory structure."""
        structure = {}
        result = self.engine._render_directory_structure(structure)
        assert "module/" in result
        assert ".ac.mod.md" in result
    
    def test_render_directory_structure_nested(self):
        """Test rendering nested directory structure."""
        structure = {
            "name": "test_module/",
            "children": [
                {
                    "name": "__init__.py",
                    "description": "Module init",
                    "children": []
                },
                {
                    "name": "subdir",
                    "description": "Subdirectory",
                    "children": [
                        {
                            "name": "sub.py",
                            "description": "Sub module",
                            "children": []
                        }
                    ]
                }
            ]
        }
        
        result = self.engine._render_directory_structure(structure)
        
        assert "test_module/" in result
        assert "├── __init__.py" in result or "└── __init__.py" in result
        assert "Module init" in result
        assert "subdir" in result
        assert "sub.py" in result
    
    def test_render_core_components_empty(self):
        """Test rendering empty core components."""
        components = []
        result = self.engine._render_core_components(components)
        assert "待分析的核心组件" in result
    
    def test_render_core_components_with_data(self):
        """Test rendering core components with data."""
        components = [
            {
                "name": "TestClass",
                "description": "A test class",
                "methods": [
                    {"name": "method1", "description": "First method"},
                    {"name": "method2", "description": "Second method"}
                ]
            },
            {
                "name": "UtilClass",
                "description": "Utility class",
                "methods": []
            }
        ]
        
        result = self.engine._render_core_components(components)
        
        assert "### 1. TestClass" in result
        assert "A test class" in result
        assert "**主要方法:**" in result
        assert "`method1()`" in result
        assert "First method" in result
        assert "### 2. UtilClass" in result
    
    def test_render_usage_examples_empty(self):
        """Test rendering empty usage examples."""
        examples = []
        result = self.engine._render_usage_examples(examples)
        assert "```python" in result
        assert "导入必要的模块" in result
    
    def test_render_usage_examples_with_data(self):
        """Test rendering usage examples with data."""
        examples = [
            {
                "code": "from module import Class\nobj = Class()",
                "language": "python"
            },
            {
                "code": "npm install module",
                "language": "bash"
            }
        ]
        
        result = self.engine._render_usage_examples(examples)
        
        assert "```python" in result
        assert "from module import Class" in result
        assert "```bash" in result
        assert "npm install module" in result
    
    def test_render_dependencies_empty(self):
        """Test rendering empty dependencies."""
        dependencies = []
        result = self.engine._render_dependencies(dependencies)
        assert "暂无依赖其他 AC 模块" in result
    
    def test_render_dependencies_with_data(self):
        """Test rendering dependencies with data."""
        dependencies = [
            "../module1/.ac.mod.md",
            "../../module2/.ac.mod.md"
        ]
        
        result = self.engine._render_dependencies(dependencies)
        
        assert "../module1/.ac.mod.md" in result
        assert "../../module2/.ac.mod.md" in result
    
    def test_render_test_commands_empty(self):
        """Test rendering empty test commands."""
        commands = []
        result = self.engine._render_test_commands(commands)
        assert "```bash" in result
        assert "暂无测试命令" in result
    
    def test_render_test_commands_with_data(self):
        """Test rendering test commands with data."""
        commands = [
            "pytest tests/ -v",
            "make test",
            "npm test"
        ]
        
        result = self.engine._render_test_commands(commands)
        
        assert "```bash" in result
        assert "pytest tests/ -v" in result
        assert "make test" in result
        assert "npm test" in result
    
    def test_update_existing_content(self):
        """Test updating existing documentation content."""
        existing_content = """# test_module

Original description

## 目录结构

```
old structure
```

## 快速开始

Original quick start

## 核心组件详解

Old components

## 依赖关系说明

Old dependencies
"""
        
        analysis = {
            "directory_structure": {
                "name": "test_module/",
                "children": [{"name": "new_file.py", "description": "New file", "children": []}]
            },
            "core_components": [
                {"name": "NewClass", "description": "New class", "methods": []}
            ]
        }
        
        updated_content = self.engine.update_existing(existing_content, analysis)
        
        # Should update directory structure
        assert "test_module/" in updated_content
        assert "new_file.py" in updated_content
        
        # Should update core components
        assert "NewClass" in updated_content
        
        # Should preserve other sections
        assert "## 快速开始" in updated_content
        assert "Original quick start" in updated_content
    
    def test_replace_section(self):
        """Test replacing a section in content."""
        content = """# Title

## Section 1
Old content here

## Section 2
Keep this content
"""
        
        new_content = "## Section 1\nNew content here\n\n"
        result = self.engine._replace_section(content, "## Section 1", "## Section 2", new_content)
        
        assert "New content here" in result
        assert "Old content here" not in result
        assert "Keep this content" in result
    
    def test_replace_section_not_found(self):
        """Test replacing a section that doesn't exist."""
        content = "# Title\n\nSome content"
        new_content = "## New Section\nNew content\n\n"
        
        result = self.engine._replace_section(content, "## Missing", "## Also Missing", new_content)
        
        # Should append the new content
        assert "## New Section" in result
        assert "New content" in result
